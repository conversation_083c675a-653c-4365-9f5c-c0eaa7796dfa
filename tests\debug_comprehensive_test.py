#!/usr/bin/env python3
"""
Debug comprehensive test
"""

import sys
import os
import json
import tempfile
import shutil
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from account_management.account_manager import A<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_comprehensive_config():
    """Debug comprehensive config loading"""
    
    test_dir = tempfile.mkdtemp()
    test_config_file = os.path.join(test_dir, "test_accounts.json")
    
    # Create comprehensive test configuration
    test_config = {
        "accounts": [
            {
                "account_id": "test_account_1",
                "account_number": ********,
                "password": "test_password",
                "server": "RoboForex-ECN",
                "strategy": "trend_following",
                "money_management": "percent_risk",
                "symbols": [
                    {"symbol": "EURUSD", "timeframe": "H1"},
                    {"symbol": "GBPUSD", "timeframe": "H4"}
                ],
                "money_management_settings": {
                    "risk_percent": 2.0,
                    "max_daily_risk_percent": 10.0
                },
                "max_daily_trades": 5,
                "max_concurrent_positions": 3,
                "trading_enabled": True
            },
            {
                "account_id": "test_account_2",
                "account_number": ********,
                "password": "test_password_2",
                "server": "RoboForex-ECN",
                "strategy": "mean_reversion",
                "money_management": "fixed_volume",
                "symbols": [
                    {"symbol": "USDJPY", "timeframe": "H1"}
                ],
                "money_management_settings": {
                    "fixed_volume": 0.01
                },
                "max_daily_trades": 10,
                "max_concurrent_positions": 2,
                "trading_enabled": True
            }
        ],
        "groups": [
            {
                "group_id": "forex_major",
                "account_ids": ["test_account_1", "test_account_2"],
                "strategy": "trend_following",
                "money_management": "percent_risk"
            }
        ]
    }
    
    with open(test_config_file, 'w') as f:
        json.dump(test_config, f)
    
    try:
        account_manager = AccountManager(config_path=test_config_file)
        print(f"Config file exists: {os.path.exists(test_config_file)}")
        print(f"Config file path: {test_config_file}")
        
        result = account_manager.load_accounts()
        print(f"Load result: {result}")
        print(f"Number of accounts: {len(account_manager.accounts)}")
        print(f"Number of groups: {len(account_manager.account_groups)}")
        
        if account_manager.accounts:
            for account_id, account in account_manager.accounts.items():
                print(f"Account: {account_id}")
                print(f"  Number: {account.account_number}")
                print(f"  Strategy: {account.strategy_type}")
                print(f"  Money Management: {account.money_management_type}")
                print(f"  Symbols: {account.symbols}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        shutil.rmtree(test_dir)

if __name__ == "__main__":
    test_comprehensive_config()
