#!/usr/bin/env python3
"""
Trading System Launcher - Starts the AI-driven trading system
This version works around import issues by running as a module
"""

import os
import sys
import subprocess
from pathlib import Path
from dotenv import load_dotenv

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("🔍 Checking Prerequisites...")
    
    # Load environment variables
    load_dotenv()
    
    # Check environment variables
    qwen_key = os.getenv('QWEN_API_KEY')
    mt5_path = os.getenv('MT5_PATH')
    
    if not qwen_key:
        print("❌ QWEN_API_KEY not found in .env file")
        print("Please add your Qwen API key to the .env file:")
        print("QWEN_API_KEY=your_api_key_here")
        return False
    
    if not mt5_path:
        print("❌ MT5_PATH not found in .env file")
        print("Please add MetaTrader 5 path to the .env file:")
        print("MT5_PATH=C:\\Program Files\\MetaTrader 5\\terminal64.exe")
        return False
    
    # Check if MT5 is running
    if not os.path.exists(mt5_path):
        print(f"❌ MetaTrader 5 not found at: {mt5_path}")
        return False
    
    # Check configuration file
    config_file = "config/accounts.json"
    if not os.path.exists(config_file):
        print(f"❌ Account configuration not found: {config_file}")
        return False
    
    print("✅ All prerequisites met")
    return True

def start_system():
    """Start the trading system"""
    print("\n🚀 Starting AI-Driven Trading System...")
    
    # Change to src directory and run as module
    src_dir = Path(__file__).parent / "src"
    
    try:
        # Set environment variables for Python path
        env = os.environ.copy()
        env['PYTHONPATH'] = str(src_dir)
        
        # Create a simple main module in src
        main_content = '''
import asyncio
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from dotenv import load_dotenv
from logging_system.logger import setup_logger, get_logger

async def main():
    """Main trading system entry point"""
    load_dotenv()
    
    logger = setup_logger()
    logger.info("AI-Driven Trading System Starting...")
    
    try:
        # Import components
        from account_management.account_manager import AccountManager
        from signal_generation.signal_generator import SignalGenerator  
        from trade_management.trade_manager import TradeManager
        
        logger.info("All components imported successfully")

        # Initialize account manager
        account_manager = AccountManager()
        if not account_manager.load_accounts():
            logger.error("Failed to load accounts")
            return

        logger.info(f"Loaded {len(account_manager.accounts)} accounts")

        # Initialize signal generator and trade manager
        signal_generator = SignalGenerator(account_manager)
        trade_manager = TradeManager(account_manager)

        logger.info("Components initialized")
        logger.info("Starting trading operations...")
        
        # Start the system
        await asyncio.gather(
            signal_generator.start(),
            trade_manager.start()
        )
        
    except KeyboardInterrupt:
        logger.info("System shutdown requested")
    except Exception as e:
        logger.error(f"System error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
'''
        
        # Write the main module
        main_file = src_dir / "run_main.py"
        with open(main_file, 'w') as f:
            f.write(main_content)
        
        print("Trading system module created")
        print("Launching trading system...")
        
        # Run the system
        result = subprocess.run([
            sys.executable, "run_main.py"
        ], cwd=src_dir, env=env, capture_output=False)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Failed to start system: {e}")
        return False

def main():
    """Main launcher function"""
    print("🤖 AI-Driven Trading System Launcher")
    print("=" * 50)
    
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above.")
        return False
    
    print("\n⚠️  IMPORTANT SAFETY NOTICE:")
    print("- This system will trade with REAL MONEY")
    print("- Make sure MetaTrader 5 is running")
    print("- Start with small position sizes")
    print("- Monitor the system closely")
    
    response = input("\nDo you want to start the trading system? (yes/no): ").lower().strip()
    
    if response in ['yes', 'y']:
        return start_system()
    else:
        print("Trading system startup cancelled.")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
