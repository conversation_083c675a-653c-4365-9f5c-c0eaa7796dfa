#!/usr/bin/env python3
"""
Simple test to show how risk percent should work
"""

import sys
sys.path.append('src')

from money_management.percent_risk import PercentRiskStrategy
from money_management.base_strategy import AccountInfo

def test_current_account():
    """Test with your current account settings"""
    print("=" * 60)
    print("TESTING YOUR CURRENT ACCOUNT SETUP")
    print("=" * 60)
    
    # Your current account
    balance = 80.0  # $80 account
    account_info = AccountInfo(
        balance=balance,
        equity=balance,
        margin=0,
        free_margin=balance/2,
        margin_level=1000,
        currency="USD",
        leverage=500
    )
    
    # Your current config
    config = {
        "risk_percent": 0.5  # 0.5% risk
    }
    
    # Market data for EURUSD
    market_data = {
        "pip_value": 10.0,  # $10 per pip per lot
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    strategy = PercentRiskStrategy(config)
    
    print(f"Account Balance: ${balance}")
    print(f"Risk Percent: {config['risk_percent']}%")
    print(f"Expected Risk Amount: ${balance * (config['risk_percent']/100):.2f}")
    print()
    
    # Test different stop loss scenarios
    scenarios = [
        {"name": "10 pips SL", "entry": 1.0850, "sl": 1.0840},
        {"name": "20 pips SL", "entry": 1.0850, "sl": 1.0830},
        {"name": "30 pips SL", "entry": 1.0850, "sl": 1.0820},
        {"name": "40 pips SL", "entry": 1.0850, "sl": 1.0810},
        {"name": "50 pips SL", "entry": 1.0850, "sl": 1.0800},
    ]
    
    for scenario in scenarios:
        trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=scenario["entry"],
            stop_loss=scenario["sl"],
            trade_history=[],
            market_data=market_data
        )
        
        pip_diff = abs(scenario["entry"] - scenario["sl"]) / market_data["pip_size"]
        expected_risk = balance * (config['risk_percent']/100)
        
        print(f"{scenario['name']}:")
        print(f"  Pip difference: {pip_diff:.0f} pips")
        print(f"  Calculated volume: {expected_risk / (pip_diff * market_data['pip_value']):.6f} lots")
        print(f"  Final volume: {trade_params.volume:.2f} lots")
        print(f"  Actual risk: ${trade_params.risk_amount:.2f}")
        print(f"  Risk as % of balance: {(trade_params.risk_amount/balance)*100:.2f}%")
        print()

def test_larger_account():
    """Test with larger account to show proper scaling"""
    print("=" * 60)
    print("TESTING WITH LARGER ACCOUNT ($1000)")
    print("=" * 60)
    
    balance = 1000.0
    account_info = AccountInfo(
        balance=balance,
        equity=balance,
        margin=0,
        free_margin=balance/2,
        margin_level=1000,
        currency="USD",
        leverage=500
    )
    
    config = {"risk_percent": 0.5}
    market_data = {
        "pip_value": 10.0,
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    strategy = PercentRiskStrategy(config)
    
    # Test 30 pip stop loss
    trade_params = strategy.calculate_position_size(
        account_info=account_info,
        symbol="EURUSD",
        entry_price=1.0850,
        stop_loss=1.0820,  # 30 pips
        trade_history=[],
        market_data=market_data
    )
    
    expected_risk = balance * (config['risk_percent']/100)
    
    print(f"Account Balance: ${balance}")
    print(f"Risk Percent: {config['risk_percent']}%")
    print(f"Expected Risk: ${expected_risk:.2f}")
    print(f"30 pips SL:")
    print(f"  Calculated volume: {expected_risk / (30 * 10):.4f} lots")
    print(f"  Final volume: {trade_params.volume:.4f} lots")
    print(f"  Actual risk: ${trade_params.risk_amount:.2f}")
    print(f"  Risk as % of balance: {(trade_params.risk_amount/balance)*100:.2f}%")

if __name__ == "__main__":
    test_current_account()
    test_larger_account()
