#!/usr/bin/env python3
"""
Pip Calculation and Normalization Validation Test
Tests pip value calculations and price normalization for all symbols and timeframes
"""

import sys
import os
import json
from typing import Dict, Any

def test_pip_size_calculations():
    """Test pip size calculations for different symbols"""
    print("📏 TESTING PIP SIZE CALCULATIONS")
    print("=" * 50)
    
    # Standard pip sizes for different symbol types
    pip_size_standards = {
        'EURUSD': 0.0001,  # 4-decimal major pair
        'GBPUSD': 0.0001,  # 4-decimal major pair
        'USDCAD': 0.0001,  # 4-decimal major pair
        'AUDUSD': 0.0001,  # 4-decimal major pair
        'NZDUSD': 0.0001,  # 4-decimal major pair
        'USDJPY': 0.01,    # 2-decimal JPY pair
        'EURJPY': 0.01,    # 2-decimal JPY pair
        'GBPJPY': 0.01,    # 2-decimal JPY pair
        'AUDJPY': 0.01,    # 2-decimal JPY pair
        'CADJPY': 0.01,    # 2-decimal JPY pair
        'XAUUSD': 0.01,    # Gold (2-decimal)
        'XAGUSD': 0.001,   # Silver (3-decimal)
    }
    
    def get_pip_size(symbol: str) -> float:
        """Calculate pip size for a symbol"""
        if 'JPY' in symbol:
            return 0.01
        elif symbol.startswith('XAU'):  # Gold
            return 0.01
        elif symbol.startswith('XAG'):  # Silver
            return 0.001
        else:
            return 0.0001  # Standard major pairs
    
    all_passed = True
    
    for symbol, expected_pip_size in pip_size_standards.items():
        calculated_pip_size = get_pip_size(symbol)
        
        print(f"  {symbol}:")
        print(f"    Expected: {expected_pip_size}")
        print(f"    Calculated: {calculated_pip_size}")
        
        is_correct = calculated_pip_size == expected_pip_size
        print(f"    Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
        
        if not is_correct:
            all_passed = False
    
    return all_passed

def test_pip_value_calculations():
    """Test pip value calculations for position sizing"""
    print("\n💰 TESTING PIP VALUE CALCULATIONS")
    print("=" * 50)
    
    # Standard pip values (approximate, for 1 standard lot)
    pip_value_standards = {
        'EURUSD': 10.0,   # $10 per pip for 1 lot
        'GBPUSD': 10.0,   # $10 per pip for 1 lot
        'USDCAD': 10.0,   # $10 per pip for 1 lot (approximately)
        'AUDUSD': 10.0,   # $10 per pip for 1 lot
        'USDJPY': 6.67,   # Approximately $6.67 per pip (varies with rate)
        'EURJPY': 6.67,   # Approximately $6.67 per pip
        'XAUUSD': 10.0,   # $10 per pip for 1 lot gold
    }
    
    def get_pip_value(symbol: str) -> float:
        """Calculate pip value for a symbol (simplified)"""
        if 'JPY' in symbol:
            return 6.67  # Approximate for JPY pairs
        elif symbol.startswith('XAU'):  # Gold
            return 10.0
        else:
            return 10.0  # Standard for USD-based pairs
    
    all_passed = True
    
    for symbol, expected_pip_value in pip_value_standards.items():
        calculated_pip_value = get_pip_value(symbol)
        
        print(f"  {symbol}:")
        print(f"    Expected: ${expected_pip_value:.2f}")
        print(f"    Calculated: ${calculated_pip_value:.2f}")
        
        # Allow 10% tolerance for pip values (they can vary with exchange rates)
        tolerance = expected_pip_value * 0.1
        is_correct = abs(calculated_pip_value - expected_pip_value) <= tolerance
        
        print(f"    Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
        
        if not is_correct:
            all_passed = False
    
    return all_passed

def test_position_size_calculations():
    """Test position size calculations with different pip values"""
    print("\n📊 TESTING POSITION SIZE CALCULATIONS")
    print("=" * 50)
    
    balance = 74.40
    risk_percent = 2.0
    risk_amount = balance * (risk_percent / 100)
    
    print(f"Account Balance: ${balance}")
    print(f"Risk Amount: ${risk_amount:.2f}")
    
    # Test scenarios with different symbols and stop distances
    test_scenarios = [
        {
            'symbol': 'EURUSD',
            'entry_price': 1.0850,
            'stop_loss': 1.0820,
            'pip_size': 0.0001,
            'pip_value': 10.0,
            'description': 'EURUSD 30 pip stop'
        },
        {
            'symbol': 'USDJPY',
            'entry_price': 149.85,
            'stop_loss': 149.65,
            'pip_size': 0.01,
            'pip_value': 6.67,
            'description': 'USDJPY 20 pip stop'
        },
        {
            'symbol': 'GBPUSD',
            'entry_price': 1.2650,
            'stop_loss': 1.2630,
            'pip_size': 0.0001,
            'pip_value': 10.0,
            'description': 'GBPUSD 20 pip stop'
        },
        {
            'symbol': 'XAUUSD',
            'entry_price': 2025.50,
            'stop_loss': 2020.50,
            'pip_size': 0.01,
            'pip_value': 10.0,
            'description': 'Gold 500 pip stop'
        }
    ]
    
    all_passed = True
    
    for scenario in test_scenarios:
        print(f"\n  {scenario['description']}:")
        
        # Calculate pip distance
        pip_distance = abs(scenario['entry_price'] - scenario['stop_loss']) / scenario['pip_size']
        
        # Calculate required volume for target risk
        required_volume = risk_amount / (pip_distance * scenario['pip_value'])
        
        # Apply minimum volume constraint
        min_volume = 0.01
        actual_volume = max(required_volume, min_volume)
        
        # Calculate actual risk with volume constraints
        actual_risk = pip_distance * scenario['pip_value'] * actual_volume
        
        print(f"    Entry: {scenario['entry_price']}")
        print(f"    Stop Loss: {scenario['stop_loss']}")
        print(f"    Pip Distance: {pip_distance:.1f}")
        print(f"    Required Volume: {required_volume:.4f}")
        print(f"    Actual Volume: {actual_volume:.2f}")
        print(f"    Actual Risk: ${actual_risk:.2f}")
        
        # Validate calculations
        volume_reasonable = actual_volume >= min_volume
        pip_calc_correct = pip_distance > 0
        risk_reasonable = actual_risk <= risk_amount * 20  # Allow up to 20x for small accounts
        
        scenario_passed = volume_reasonable and pip_calc_correct and risk_reasonable
        
        print(f"    Volume >= Min: {'✅' if volume_reasonable else '❌'}")
        print(f"    Pip Calc Correct: {'✅' if pip_calc_correct else '❌'}")
        print(f"    Risk Reasonable: {'✅' if risk_reasonable else '❌'}")
        print(f"    Result: {'✅ PASS' if scenario_passed else '❌ FAIL'}")
        
        if not scenario_passed:
            all_passed = False
    
    return all_passed

def test_price_normalization():
    """Test price normalization for different symbols"""
    print("\n🎯 TESTING PRICE NORMALIZATION")
    print("=" * 50)
    
    # Test price normalization scenarios
    normalization_scenarios = [
        {
            'symbol': 'EURUSD',
            'raw_price': 1.********,
            'expected_digits': 5,
            'expected_normalized': 1.08501,
            'description': 'EURUSD 5-digit precision'
        },
        {
            'symbol': 'USDJPY',
            'raw_price': 149.856789,
            'expected_digits': 3,
            'expected_normalized': 149.857,
            'description': 'USDJPY 3-digit precision'
        },
        {
            'symbol': 'GBPUSD',
            'raw_price': 1.26498765,
            'expected_digits': 5,
            'expected_normalized': 1.26499,
            'description': 'GBPUSD 5-digit precision'
        },
        {
            'symbol': 'XAUUSD',
            'raw_price': 2025.567890,
            'expected_digits': 2,
            'expected_normalized': 2025.57,
            'description': 'Gold 2-digit precision'
        }
    ]
    
    def normalize_price(symbol: str, price: float) -> float:
        """Normalize price based on symbol type"""
        if 'JPY' in symbol:
            return round(price, 3)  # 3 decimal places for JPY pairs
        elif symbol.startswith('XAU'):  # Gold
            return round(price, 2)  # 2 decimal places for gold
        else:
            return round(price, 5)  # 5 decimal places for major pairs
    
    all_passed = True
    
    for scenario in normalization_scenarios:
        normalized_price = normalize_price(scenario['symbol'], scenario['raw_price'])
        
        print(f"  {scenario['description']}:")
        print(f"    Raw Price: {scenario['raw_price']}")
        print(f"    Expected: {scenario['expected_normalized']}")
        print(f"    Normalized: {normalized_price}")
        
        is_correct = abs(normalized_price - scenario['expected_normalized']) < 0.000001
        print(f"    Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
        
        if not is_correct:
            all_passed = False
    
    return all_passed

def test_volume_step_validation():
    """Test volume step validation"""
    print("\n📐 TESTING VOLUME STEP VALIDATION")
    print("=" * 50)
    
    # Standard volume steps for different brokers/symbols
    volume_scenarios = [
        {
            'symbol': 'EURUSD',
            'min_volume': 0.01,
            'max_volume': 100.0,
            'volume_step': 0.01,
            'test_volumes': [0.01, 0.05, 0.1, 1.0, 10.0],
            'description': 'Standard forex pair'
        },
        {
            'symbol': 'XAUUSD',
            'min_volume': 0.01,
            'max_volume': 50.0,
            'volume_step': 0.01,
            'test_volumes': [0.01, 0.1, 1.0, 5.0],
            'description': 'Gold trading'
        }
    ]
    
    def validate_volume(volume: float, min_vol: float, max_vol: float, step: float) -> bool:
        """Validate if volume is within acceptable range and step"""
        if volume < min_vol or volume > max_vol:
            return False
        
        # Check if volume is a multiple of step
        remainder = (volume - min_vol) % step
        return abs(remainder) < 0.0001  # Small tolerance for floating point
    
    all_passed = True
    
    for scenario in volume_scenarios:
        print(f"\n  {scenario['description']} ({scenario['symbol']}):")
        print(f"    Min Volume: {scenario['min_volume']}")
        print(f"    Max Volume: {scenario['max_volume']}")
        print(f"    Volume Step: {scenario['volume_step']}")
        
        for volume in scenario['test_volumes']:
            is_valid = validate_volume(
                volume, 
                scenario['min_volume'], 
                scenario['max_volume'], 
                scenario['volume_step']
            )
            
            print(f"    Volume {volume}: {'✅ VALID' if is_valid else '❌ INVALID'}")
            
            if not is_valid:
                all_passed = False
    
    return all_passed

def main():
    """Main test function"""
    print("📏 PIP CALCULATION AND NORMALIZATION TEST")
    print("=" * 60)
    
    tests = [
        ("Pip Size Calculations", test_pip_size_calculations),
        ("Pip Value Calculations", test_pip_value_calculations),
        ("Position Size Calculations", test_position_size_calculations),
        ("Price Normalization", test_price_normalization),
        ("Volume Step Validation", test_volume_step_validation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎯 TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = [r for r in results if r[1]]
    total_tests = len(results)
    
    for test_name, passed in results:
        print(f"{test_name}: {'✅ PASS' if passed else '❌ FAIL'}")
    
    print(f"\nOverall: {len(passed_tests)}/{total_tests} tests passed")
    
    overall_success = len(passed_tests) == total_tests
    print(f"Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
