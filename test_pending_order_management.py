#!/usr/bin/env python3
"""
Test script for pending order management functionality
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from mt5_integration.mt5_client import MT5Client
from account_management.account_manager import AccountManager
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

async def test_pending_order_management():
    """Test pending order management functions"""
    print("🚀 TESTING PENDING ORDER MANAGEMENT")
    print("=" * 60)
    
    # Initialize MT5 client
    mt5_client = MT5Client()
    
    try:
        # Initialize MT5
        if not mt5_client.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        print("✅ MT5 initialized successfully")
        
        # Load account manager and get first account
        account_manager = AccountManager()
        accounts = account_manager.get_all_accounts()
        
        if not accounts:
            print("❌ No accounts found")
            return False
        
        account = accounts[0]
        print(f"🔐 Testing with account: {account.account_id}")
        
        # Login to account
        if not mt5_client.login(account):
            print(f"❌ Failed to login to account {account.account_id}")
            return False
        
        print("✅ Account login successful")
        
        # Test 1: Get pending orders
        print("\n📋 TEST 1: Getting pending orders...")
        pending_orders = mt5_client.get_pending_orders()
        print(f"✅ Found {len(pending_orders)} pending orders")
        
        if pending_orders:
            print("📋 Current pending orders:")
            for i, order in enumerate(pending_orders, 1):
                print(f"   {i}. Ticket: {order['ticket']} | {order['symbol']} | Type: {order['type']} | Price: {order['price_open']:.5f}")
        
        # Test 2: Test order modification (if orders exist)
        if pending_orders:
            test_order = pending_orders[0]
            ticket = test_order['ticket']
            original_price = test_order['price_open']
            
            print(f"\n🔧 TEST 2: Testing order modification for ticket {ticket}...")
            
            # Calculate a small price adjustment (1 pip)
            pip_size = 0.0001 if 'JPY' not in test_order['symbol'] else 0.01
            new_price = original_price + pip_size
            
            print(f"   Original price: {original_price:.5f}")
            print(f"   New price: {new_price:.5f}")
            
            # Test modify_order function
            success = mt5_client.modify_order(
                ticket=ticket,
                price=new_price
            )
            
            if success:
                print("✅ Order modification successful")
                
                # Verify the modification
                updated_orders = mt5_client.get_pending_orders()
                updated_order = next((o for o in updated_orders if o['ticket'] == ticket), None)
                
                if updated_order and abs(updated_order['price_open'] - new_price) < pip_size/10:
                    print("✅ Price modification verified")
                else:
                    print("⚠️ Price modification not reflected (may take time)")
                
                # Restore original price
                print("🔄 Restoring original price...")
                restore_success = mt5_client.modify_order(
                    ticket=ticket,
                    price=original_price
                )
                
                if restore_success:
                    print("✅ Original price restored")
                else:
                    print("⚠️ Failed to restore original price")
                    
            else:
                print("❌ Order modification failed")
        
        else:
            print("\n⚠️ No pending orders found to test modification")
        
        # Test 3: Test order cancellation (create a test order first)
        print(f"\n🗑️ TEST 3: Testing order cancellation...")
        
        # Get current market price for a test order
        market_data = mt5_client.get_market_data("EURUSD", "M1", 1)
        if market_data:
            current_price = market_data['current_price']
            # Place a test order far from market (won't execute)
            test_price = current_price + 0.01  # 100 pips away
            
            print(f"   Placing test order at {test_price:.5f} (current: {current_price:.5f})")
            
            # Place a BUY_LIMIT order
            test_ticket = mt5_client.place_order(
                symbol="EURUSD",
                action="BUY_LIMIT",
                volume=0.01,
                price=test_price,
                comment="Test order for cancellation"
            )
            
            if test_ticket:
                print(f"✅ Test order placed with ticket: {test_ticket}")
                
                # Wait a moment for order to be processed
                await asyncio.sleep(2)
                
                # Test cancellation
                cancel_success = mt5_client.cancel_order(test_ticket)
                
                if cancel_success:
                    print("✅ Order cancellation successful")
                else:
                    print("❌ Order cancellation failed")
                    
            else:
                print("❌ Failed to place test order")
        
        print("\n🎯 PENDING ORDER MANAGEMENT TESTS COMPLETED")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False
        
    finally:
        # Cleanup
        mt5_client.shutdown()
        print("🔒 MT5 connection closed")

async def main():
    """Main test function"""
    success = await test_pending_order_management()
    
    if success:
        print("\n🎉 ALL TESTS COMPLETED SUCCESSFULLY")
    else:
        print("\n❌ SOME TESTS FAILED")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
