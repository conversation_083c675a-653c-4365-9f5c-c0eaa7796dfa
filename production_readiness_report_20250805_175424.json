{"timestamp": "2025-08-05T17:54:24.150429", "test_results": [{"name": "AI Stop Loss Validation", "file": "test_simple_ai_stop_loss.py", "critical": true, "description": "Validates AI makes dynamic stop loss decisions", "passed": false, "output": "", "error": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_simple_ai_stop_loss.py\", line 310, in <module>\n    success = main()\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_simple_ai_stop_loss.py\", line 272, in main\n    print(\"\\U0001f916 AI STOP LOSS VALIDATION TEST\")\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f916' in position 0: character maps to <undefined>\n"}, {"name": "Risk Management Compliance", "file": "test_simple_risk_management.py", "critical": true, "description": "Validates risk management enforcement", "passed": false, "output": "", "error": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_simple_risk_management.py\", line 286, in <module>\n    success = main()\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_simple_risk_management.py\", line 248, in main\n    print(\"\\U0001f6e1\\ufe0f RISK MANAGEMENT COMPLIANCE TEST\")\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>\n"}, {"name": "Pip Calculation Validation", "file": "test_pip_calculation_validation.py", "critical": false, "description": "Validates pip calculations and normalization", "passed": false, "output": "", "error": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_pip_calculation_validation.py\", line 358, in <module>\n    success = main()\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_pip_calculation_validation.py\", line 319, in main\n    print(\"\\U0001f4cf PIP CALCULATION AND NORMALIZATION TEST\")\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f4cf' in position 0: character maps to <undefined>\n"}, {"name": "End-to-End Integration", "file": "test_end_to_end_integration.py", "critical": true, "description": "Validates complete system integration", "passed": false, "output": "", "error": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_end_to_end_integration.py\", line 461, in main\n    await validator.run_comprehensive_validation()\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_end_to_end_integration.py\", line 35, in run_comprehensive_validation\n    print(\"\\U0001f504 END-TO-END INTEGRATION VALIDATION\")\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f504' in position 0: character maps to <undefined>\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_end_to_end_integration.py\", line 469, in <module>\n    success = asyncio.run(main())\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\runners.py\", line 44, in run\n    return loop.run_until_complete(main)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"K:\\desktop\\augment\\augment2\\augment\\trade\\test_end_to_end_integration.py\", line 463, in main\n    print(f\"\\u274c Test failed with error: {e}\")\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u274c' in position 0: character maps to <undefined>\n"}], "config_checks": [["Account Configuration", true, "All required settings present"], ["Environment Variables", false, "Missing: ['OPENAI_API_KEY']"], ["Directory Structure", true, "All required directories present"]], "risk_checks": [["Risk Percent", true, "2.0% - within safe range"], ["Daily Loss Limit", true, "$3.0 - reasonable limit"], ["Position Limit", true, "2 positions - reasonable"]], "recommendation": "BLOCK_PRODUCTION", "summary": {"total_tests": 4, "passed_tests": 0, "critical_tests": 3, "critical_passed": 0, "config_passed": 2, "risk_passed": 3}}