#!/usr/bin/env python3
"""
Verification test for session management fixes
Tests the specific scenario that was failing in the original logs
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from mt5_integration.mt5_client import MT5Client
from mt5_integration.session_manager import session_manager
from account_management.models import TradingAccount
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

async def test_session_fix():
    """Test the specific session management scenario that was failing"""
    print("🔧 TESTING SESSION MANAGEMENT FIXES")
    print("=" * 60)
    
    try:
        # Create a test account (using the working demo account)
        test_account = TradingAccount(
            account_id="demo1",
            account_number=********,
            server="RoboForex-ECN",
            username="********",
            password="test_password",  # This won't be used since we're already logged in
            strategy_type="mean_reversion",
            money_management_type="martingale",
            symbols=[{"symbol": "EURUSD", "timeframe": "M15"}],
            timeframes=["M15"]
        )
        
        # Initialize MT5 client
        mt5_client = MT5Client()
        if not mt5_client.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        print("✅ MT5 initialized")
        
        # Test the session management scenario
        print("\n🔄 Testing session management...")
        
        # Use the session manager context (this is what was failing)
        async with session_manager.account_session(test_account, mt5_client) as managed_client:
            print("✅ Session acquired successfully")
            print(f"   Current account: {managed_client.current_account.account_id if managed_client.current_account else 'None'}")
            print(f"   Connected: {managed_client.connected}")
            
            # Test the operations that were failing
            print("\n📊 Testing data retrieval operations...")
            
            # Test get_positions (was failing with "No account logged in")
            positions = managed_client.get_positions()
            print(f"✅ get_positions(): Retrieved {len(positions)} positions")
            
            # Test get_pending_orders (was failing with "MT5 not connected")
            pending_orders = managed_client.get_pending_orders()
            print(f"✅ get_pending_orders(): Retrieved {len(pending_orders)} pending orders")
            
            # Test account info
            account_info = managed_client.get_account_info()
            if account_info:
                print(f"✅ get_account_info(): Balance ${account_info.balance:.2f}")
            else:
                print("❌ get_account_info(): Failed")
                return False
        
        print("\n✅ Session released successfully")
        
        # Test multiple session acquisitions (simulate the scheduler scenario)
        print("\n🔄 Testing multiple session acquisitions...")
        
        for i in range(3):
            print(f"\n   Test {i+1}/3:")
            async with session_manager.account_session(test_account, mt5_client) as managed_client:
                positions = managed_client.get_positions()
                orders = managed_client.get_pending_orders()
                print(f"   ✅ Session {i+1}: {len(positions)} positions, {len(orders)} orders")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        logger.error(f"Test error: {e}", exc_info=True)
        return False
        
    finally:
        # Cleanup
        if 'mt5_client' in locals():
            mt5_client.shutdown()
        print("\n🔒 MT5 connection closed")

async def main():
    """Main test function"""
    success = await test_session_fix()
    
    if success:
        print("\n🎉 SESSION MANAGEMENT FIXES VERIFIED")
        print("✅ No more 'No account logged in' errors")
        print("✅ No more 'MT5 not connected' errors") 
        print("✅ Session state properly synchronized")
        print("✅ Multiple session acquisitions working")
    else:
        print("\n❌ SESSION MANAGEMENT FIXES NEED MORE WORK")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
