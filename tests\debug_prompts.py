#!/usr/bin/env python3
"""
Debug AI prompts to see what they actually contain
"""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from strategies.trend_following import TrendFollowingStrategy
from strategies.mean_reversion import MeanReversionStrategy
from strategies.breakout import BreakoutStrategy
from strategies.base_strategy import MarketData
from money_management.percent_risk import PercentRiskStrategy
from money_management.base_strategy import AccountInfo

def debug_prompts():
    """Debug what's actually in the prompts"""
    
    # Set up test data
    market_data = MarketData(
        symbol="EURUSD",
        timeframe="H1",
        candles=[
            {'time': '2025-08-01 10:00:00', 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000},
        ],
        current_price=1.1030,
        spread=1.5,
        volume=1100,
        volatility=0.0015
    )
    
    account_info_dict = {
        'balance': 10000.0,
        'equity': 10000.0,
        'currency': 'USD'
    }
    
    trade_history = []
    
    # Test trend following strategy
    print("=== TREND FOLLOWING STRATEGY PROMPT ===")
    strategy = TrendFollowingStrategy({'magic_number': 12345})
    prompt = strategy.get_ai_prompt(market_data, trade_history, account_info_dict)
    print(f"Length: {len(prompt)}")
    print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
    print("\n")
    
    # Test percent risk money management
    print("=== PERCENT RISK MONEY MANAGEMENT PROMPT ===")
    account_info = AccountInfo(
        balance=10000.0,
        equity=10000.0,
        margin=1000.0,
        free_margin=9000.0,
        margin_level=1000.0,
        currency="USD",
        leverage=100
    )
    
    mm_strategy = PercentRiskStrategy({'risk_percent': 2.0})
    mm_prompt = mm_strategy.get_ai_prompt(account_info, trade_history)
    print(f"Length: {len(mm_prompt)}")
    print(mm_prompt[:500] + "..." if len(mm_prompt) > 500 else mm_prompt)
    print("\n")
    
    # Check for specific concepts
    concepts_to_check = [
        "trend following", "trend direction", "moving averages", "breakout",
        "support", "resistance", "stop loss", "take profit", "risk management",
        "drawdown", "technical analysis", "price action", "momentum"
    ]
    
    print("=== CONCEPT ANALYSIS ===")
    for concept in concepts_to_check:
        found_in_strategy = concept.lower() in prompt.lower()
        found_in_mm = concept.lower() in mm_prompt.lower()
        print(f"{concept:20} | Strategy: {found_in_strategy:5} | MM: {found_in_mm:5}")

if __name__ == "__main__":
    debug_prompts()
