#!/usr/bin/env python3
"""
Comprehensive Test Suite for AI-Driven Trading System
"""

import unittest
import asyncio
import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import json

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from account_management.account_manager import AccountManager
from money_management.percent_risk import PercentRiskStrategy
from strategies.trend_following import TrendFollowingStrategy
from ai_integration.qwen_client import QwenClient
from ai_integration.prompt_builder import PromptBuilder
from mt5_integration.mt5_client import MT5Client
from signal_generation.signal_generator import SignalGenerator
from trade_management.trade_manager import TradeManager

class TestAccountManager(unittest.TestCase):
    """Test Account Manager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_config_file = "test_accounts.json"
        self.account_manager = AccountManager(config_path=self.test_config_file)
        
        # Create test configuration
        test_config = {
            "accounts": [
                {
                    "account_id": "test_account_1",
                    "account_number": ********,
                    "password": "test_password",
                    "server": "RoboForex-ECN",
                    "strategy": "trend_following",
                    "money_management": "percent_risk",
                    "symbols": [
                        {"symbol": "EURUSD", "timeframe": "H1"}
                    ],
                    "money_management_settings": {
                        "risk_percent": 2.0
                    }
                }
            ]
        }
        
        with open(self.test_config_file, 'w') as f:
            json.dump(test_config, f)
    
    def tearDown(self):
        """Clean up test environment"""
        if os.path.exists(self.test_config_file):
            os.remove(self.test_config_file)
    
    def test_load_accounts(self):
        """Test account loading from configuration"""
        result = self.account_manager.load_accounts()
        self.assertTrue(result)
        self.assertEqual(len(self.account_manager.accounts), 1)
        self.assertIn("test_account_1", self.account_manager.accounts)
    
    def test_strategy_factory(self):
        """Test strategy factory"""
        from strategies.base_strategy import StrategyType
        strategy = self.account_manager.strategy_factory.create_strategy(StrategyType.TREND_FOLLOWING, {'magic_number': 12345})
        self.assertIsInstance(strategy, TrendFollowingStrategy)
    
    def test_money_management_factory(self):
        """Test money management factory"""
        from money_management.base_strategy import MoneyManagementType
        mm_strategy = self.account_manager.money_management_factory.create_strategy(MoneyManagementType.PERCENT_RISK, {'risk_percent': 2.0})
        self.assertIsInstance(mm_strategy, PercentRiskStrategy)

class TestMoneyManagement(unittest.TestCase):
    """Test Money Management strategies"""
    
    def test_percent_risk_strategy(self):
        """Test percent risk calculation"""
        from money_management.base_strategy import AccountInfo
        
        strategy = PercentRiskStrategy({'risk_percent': 2.0})
        
        account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=[],
            market_data={'pip_value': 1.0, 'pip_size': 0.0001, 'min_volume': 0.01, 'max_volume': 100.0}
        )
        
        self.assertIsNotNone(trade_params)
        self.assertGreater(trade_params.volume, 0)

class TestAIIntegration(unittest.TestCase):
    """Test AI Integration components"""
    
    def test_prompt_builder(self):
        """Test prompt builder functionality"""
        prompt_builder = PromptBuilder()
        
        # Mock strategy and money management
        strategy = Mock()
        strategy.get_ai_prompt.return_value = "Test strategy prompt"
        
        money_management = Mock()
        money_management.get_ai_prompt.return_value = "Test money management prompt"
        
        # Mock market data
        from strategies.base_strategy import MarketData
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            current_price=1.1000,
            candles=[],
            volume=1000,
            spread=1.5,
            volatility=0.0015
        )
        
        # Mock account info
        from money_management.base_strategy import AccountInfo
        account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        prompt = prompt_builder.build_trading_prompt(
            strategy=strategy,
            money_management=money_management,
            market_data=market_data,
            trade_history=[],
            account_info=account_info
        )
        
        self.assertIsInstance(prompt, str)
        self.assertIn("EURUSD", prompt)

class TestMT5Integration(unittest.TestCase):
    """Test MT5 Integration (mocked)"""
    
    @patch('MetaTrader5.initialize')
    @patch('MetaTrader5.login')
    @patch('MetaTrader5.terminal_info')
    @patch('MetaTrader5.account_info')
    def test_mt5_client_login(self, mock_account_info, mock_terminal_info, mock_login, mock_initialize):
        """Test MT5 client login functionality"""
        mock_initialize.return_value = True
        mock_login.return_value = True
        mock_terminal_info.return_value = Mock(connected=True, build=1234)
        mock_account_info.return_value = Mock(
            login=********,
            server="RoboForex-ECN",
            balance=10000.0
        )

        mt5_client = MT5Client()
        # Initialize first
        mt5_client.initialize()

        # Mock account
        account = Mock()
        account.account_number = ********
        account.password = "test_password"
        account.server = "RoboForex-ECN"
        account.account_id = "test_account"

        result = mt5_client.login(account)
        self.assertTrue(result)

class TestSignalGeneration(unittest.IsolatedAsyncioTestCase):
    """Test Signal Generation system"""
    
    async def test_signal_generator_initialization(self):
        """Test signal generator initialization"""
        # Mock account manager
        account_manager = Mock()
        account_manager.accounts = {}
        
        signal_generator = SignalGenerator(account_manager)
        self.assertIsNotNone(signal_generator)

class TestTradeManagement(unittest.IsolatedAsyncioTestCase):
    """Test Trade Management system"""
    
    async def test_trade_manager_initialization(self):
        """Test trade manager initialization"""
        # Mock account manager
        account_manager = Mock()
        account_manager.accounts = {}
        
        trade_manager = TradeManager(account_manager)
        self.assertIsNotNone(trade_manager)

class TestSystemIntegration(unittest.IsolatedAsyncioTestCase):
    """Test full system integration"""
    
    async def test_system_components_integration(self):
        """Test that all components can work together"""
        # This is a basic integration test
        # In a real scenario, you'd test with actual data flows
        
        # Create test configuration
        test_config_file = "test_integration_accounts.json"
        test_config = {
            "accounts": [
                {
                    "account_id": "integration_test",
                    "account_number": ********,
                    "password": "test_password",
                    "server": "RoboForex-ECN",
                    "strategy": "trend_following",
                    "money_management": "percent_risk",
                    "symbols": [
                        {"symbol": "EURUSD", "timeframe": "H1"}
                    ],
                    "money_management_settings": {
                        "risk_percent": 2.0
                    }
                }
            ]
        }
        
        with open(test_config_file, 'w') as f:
            json.dump(test_config, f)
        
        try:
            # Initialize components
            account_manager = AccountManager(config_path=test_config_file)
            account_manager.load_accounts()
            
            signal_generator = SignalGenerator(account_manager)
            trade_manager = TradeManager(account_manager)
            
            # Verify components are properly initialized
            self.assertIsNotNone(signal_generator)
            self.assertIsNotNone(trade_manager)
            self.assertEqual(len(account_manager.accounts), 1)
            
        finally:
            # Clean up
            if os.path.exists(test_config_file):
                os.remove(test_config_file)

def run_tests():
    """Run all tests"""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestAccountManager))
    test_suite.addTest(unittest.makeSuite(TestMoneyManagement))
    test_suite.addTest(unittest.makeSuite(TestAIIntegration))
    test_suite.addTest(unittest.makeSuite(TestMT5Integration))
    test_suite.addTest(unittest.makeSuite(TestSignalGeneration))
    test_suite.addTest(unittest.makeSuite(TestTradeManagement))
    test_suite.addTest(unittest.makeSuite(TestSystemIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
