"""
Qwen AI API Client for Trading Decisions
"""

import os
import json
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from datetime import datetime

from logging_system.logger import get_logger

logger = get_logger(__name__)

class QwenClient:
    """Client for Qwen AI API integration"""
    
    def __init__(self):
        self.api_key = os.getenv('QWEN_API_KEY')
        self.api_url = os.getenv('QWEN_API_URL', 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1')
        self.model = os.getenv('AI_MODEL', 'qwen-max-2025-01-25')
        self.max_concurrent_requests = int(os.getenv('MAX_CONCURRENT_REQUESTS', '5'))

        # Use OpenAI-compatible endpoint format
        if not self.api_url.endswith('/chat/completions'):
            self.api_url = self.api_url.rstrip('/') + '/chat/completions'
        
        if not self.api_key:
            raise ValueError("QWEN_API_KEY environment variable is required")
        
        self.session: Optional[aiohttp.ClientSession] = None
        self.semaphore = asyncio.Semaphore(self.max_concurrent_requests)
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def generate_trading_decision(
        self, 
        prompt: str, 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate trading decision using Qwen AI"""
        
        async with self.semaphore:
            try:
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }
                
                # Use OpenAI-compatible format for international endpoint
                payload = {
                    'model': self.model,
                    'messages': [
                        {
                            'role': 'system',
                            'content': self._get_system_prompt()
                        },
                        {
                            'role': 'user',
                            'content': prompt
                        }
                    ],
                    'temperature': 0.3,  # Lower temperature for more consistent trading decisions
                    'max_tokens': 2000,
                    'top_p': 0.8
                }
                
                if not self.session:
                    self.session = aiohttp.ClientSession()
                
                async with self.session.post(self.api_url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return self._parse_ai_response(result, context)
                    else:
                        error_text = await response.text()
                        logger.error(f"Qwen API error {response.status}: {error_text}")
                        return self._get_error_response(f"API Error: {response.status}")
                        
            except asyncio.TimeoutError:
                logger.error("Qwen API request timeout")
                return self._get_error_response("Request timeout")
            except Exception as e:
                logger.error(f"Error calling Qwen API: {e}")
                return self._get_error_response(str(e))
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for trading AI"""
        return """
You are an expert trading AI assistant specializing in forex and financial markets analysis.

Your role is to:
1. Analyze market data and provide specific trading recommendations
2. Generate clear BUY/SELL/HOLD/CLOSE signals with reasoning
3. Provide specific entry prices, stop losses, and take profit levels
4. Assess risk levels and confidence in your recommendations
5. Consider money management strategies in your decisions

Response Format Requirements:
Always respond with a JSON object containing:
{
    "action": "BUY|SELL|HOLD|CLOSE",
    "confidence": 0.0-1.0,
    "entry_price": number or null,
    "stop_loss": number or null,
    "take_profit": number or null,
    "reasoning": "detailed explanation",
    "risk_level": "LOW|MEDIUM|HIGH",
    "additional_signals": [
        {
            "action": "BUY|SELL|HOLD|CLOSE",
            "confidence": 0.0-1.0,
            "entry_price": number or null,
            "stop_loss": number or null,
            "take_profit": number or null,
            "reasoning": "explanation"
        }
    ]
}

Guidelines:
- Be conservative with high-risk strategies like Martingale
- Always provide stop losses for risk management
- Consider market volatility and spread conditions
- Avoid overtrading - quality over quantity
- Respect the money management strategy constraints
- Provide clear, actionable reasoning for each decision
"""
    
    def _parse_ai_response(self, response: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Parse AI response and extract trading signals"""
        try:
            # Extract the AI's response text (OpenAI-compatible format)
            choices = response.get('choices', [])
            if not choices:
                return self._get_error_response("No choices in response")

            message = choices[0].get('message', {})
            text = message.get('content', '')

            if not text:
                return self._get_error_response("Empty response from AI")
            
            # Try to extract JSON from the response
            try:
                # Look for JSON in the response
                start_idx = text.find('{')
                end_idx = text.rfind('}') + 1
                
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = text[start_idx:end_idx]
                    parsed_response = json.loads(json_str)
                    
                    # Validate required fields
                    required_fields = ['action', 'confidence', 'reasoning', 'risk_level']
                    if all(field in parsed_response for field in required_fields):
                        # Add metadata
                        parsed_response['timestamp'] = datetime.now().isoformat()
                        parsed_response['model'] = self.model
                        parsed_response['raw_response'] = text
                        
                        if context:
                            parsed_response['context'] = context
                        
                        return parsed_response
                    else:
                        logger.warning(f"Missing required fields in AI response: {parsed_response}")
                        return self._get_fallback_response(text)
                
                else:
                    logger.warning("No valid JSON found in AI response")
                    return self._get_fallback_response(text)
                    
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON from AI response: {e}")
                return self._get_fallback_response(text)
                
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
            return self._get_error_response(str(e))
    
    def _get_fallback_response(self, text: str) -> Dict[str, Any]:
        """Generate fallback response when parsing fails"""
        return {
            'action': 'HOLD',
            'confidence': 0.0,
            'entry_price': None,
            'stop_loss': None,
            'take_profit': None,
            'reasoning': f"AI response parsing failed. Raw response: {text[:200]}...",
            'risk_level': 'HIGH',
            'timestamp': datetime.now().isoformat(),
            'model': self.model,
            'error': 'parsing_failed',
            'raw_response': text
        }
    
    def _get_error_response(self, error_message: str) -> Dict[str, Any]:
        """Generate error response"""
        return {
            'action': 'HOLD',
            'confidence': 0.0,
            'entry_price': None,
            'stop_loss': None,
            'take_profit': None,
            'reasoning': f"Error occurred: {error_message}",
            'risk_level': 'HIGH',
            'timestamp': datetime.now().isoformat(),
            'model': self.model,
            'error': error_message
        }
