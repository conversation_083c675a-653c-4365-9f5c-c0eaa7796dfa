"""
Trade Validation System
Validates trade parameters and execution logic before placing orders
"""

import MetaTrader5 as mt5
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum

from logging_system.logger import get_logger

logger = get_logger(__name__)


class ValidationResult(Enum):
    """Validation result types"""
    VALID = "VALID"
    INVALID = "INVALID"
    RETRY_REQUIRED = "RETRY_REQUIRED"


@dataclass
class ValidationError:
    """Validation error details"""
    error_type: str
    message: str
    suggested_fix: Optional[str] = None
    retry_possible: bool = False


@dataclass
class TradeValidationResult:
    """Trade validation result"""
    result: ValidationResult
    errors: List[ValidationError]
    corrected_params: Optional[Dict[str, Any]] = None


class TradeValidator:
    """Validates trade parameters and execution logic"""
    
    def __init__(self):
        self.max_retries = 3
        self.min_sl_distance_pips = 10  # Minimum stop loss distance in pips
        self.min_tp_distance_pips = 10  # Minimum take profit distance in pips
        
    def validate_signal_execution(
        self,
        symbol: str,
        action: str,
        volume: float,
        price: Optional[float] = None,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        magic_number: int = 0
    ) -> TradeValidationResult:
        """Validate signal execution parameters"""
        logger.info(f"🔍 VALIDATION: Validating signal execution for {symbol} {action}")

        # Fix type issues - ensure all numeric parameters are floats
        try:
            if price is not None:
                if isinstance(price, (list, tuple)):
                    price = float(price[0]) if price else None
                    logger.warning(f"⚠️ VALIDATION: Price was list/tuple, converted to: {price}")
                else:
                    price = float(price)

            if stop_loss is not None:
                if isinstance(stop_loss, (list, tuple)):
                    stop_loss = float(stop_loss[0]) if stop_loss else None
                    logger.warning(f"⚠️ VALIDATION: Stop loss was list/tuple, converted to: {stop_loss}")
                else:
                    stop_loss = float(stop_loss)

            if take_profit is not None:
                if isinstance(take_profit, (list, tuple)):
                    take_profit = float(take_profit[0]) if take_profit else None
                    logger.warning(f"⚠️ VALIDATION: Take profit was list/tuple, converted to: {take_profit}")
                else:
                    take_profit = float(take_profit)

            volume = float(volume)

        except (ValueError, TypeError, IndexError) as e:
            logger.error(f"❌ VALIDATION: Type conversion error: {e}")
            errors = [ValidationError(
                error_type="TYPE_ERROR",
                message=f"Invalid parameter types: {e}",
                retry_possible=False
            )]
            return TradeValidationResult(ValidationResult.INVALID, errors)

        # Normalize prices to correct decimal places
        if price is not None:
            original_price = price
            price = self._normalize_price(symbol, price)
            if price != original_price:
                corrected_params["price"] = price
                logger.info(f"🔍 VALIDATION: Price normalized from {original_price} to {price}")

        if stop_loss is not None:
            original_sl = stop_loss
            stop_loss = self._normalize_price(symbol, stop_loss)
            if stop_loss != original_sl:
                corrected_params["stop_loss"] = stop_loss
                logger.info(f"🔍 VALIDATION: Stop loss normalized from {original_sl} to {stop_loss}")

        if take_profit is not None:
            original_tp = take_profit
            take_profit = self._normalize_price(symbol, take_profit)
            if take_profit != original_tp:
                corrected_params["take_profit"] = take_profit
                logger.info(f"🔍 VALIDATION: Take profit normalized from {original_tp} to {take_profit}")

        errors = []
        corrected_params = {}
        
        # Get symbol info for validation
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            errors.append(ValidationError(
                error_type="SYMBOL_NOT_FOUND",
                message=f"Symbol {symbol} not found or not available",
                retry_possible=False
            ))
            return TradeValidationResult(ValidationResult.INVALID, errors)
        
        # Get current tick for price validation
        tick = mt5.symbol_info_tick(symbol)
        if tick is None:
            errors.append(ValidationError(
                error_type="NO_TICK_DATA",
                message=f"Cannot get tick data for {symbol}",
                retry_possible=True
            ))
            return TradeValidationResult(ValidationResult.RETRY_REQUIRED, errors)
        
        # Validate action
        if action.upper() not in ["BUY", "SELL"]:
            errors.append(ValidationError(
                error_type="INVALID_ACTION",
                message=f"Invalid action: {action}. Must be BUY or SELL",
                retry_possible=False
            ))
        
        # Validate and correct price
        if price is None:
            if action.upper() == "BUY":
                price = tick.ask
                corrected_params["price"] = price
                logger.info(f"🔍 VALIDATION: Auto-corrected price for BUY: {price}")
            else:
                price = tick.bid
                corrected_params["price"] = price
                logger.info(f"🔍 VALIDATION: Auto-corrected price for SELL: {price}")
        
        # Validate volume
        volume_errors = self._validate_volume(symbol_info, volume)
        if volume_errors:
            # Try to correct volume
            corrected_volume = self._correct_volume(symbol_info, volume)
            if corrected_volume != volume:
                corrected_params["volume"] = corrected_volume
                logger.info(f"🔍 VALIDATION: Auto-corrected volume from {volume} to {corrected_volume}")
            else:
                errors.extend(volume_errors)
        
        # Validate stop loss
        if stop_loss is not None:
            sl_errors, corrected_sl = self._validate_stop_loss(symbol_info, action, price, stop_loss)
            if corrected_sl != stop_loss:
                corrected_params["stop_loss"] = corrected_sl
                logger.info(f"🔍 VALIDATION: Auto-corrected stop loss from {stop_loss} to {corrected_sl}")
            elif sl_errors:
                errors.extend(sl_errors)
        
        # Validate take profit (handle both single and multiple TPs)
        if take_profit is not None:
            # Handle list of take profits (convert to single for validation)
            if isinstance(take_profit, list) and take_profit:
                # Use first TP for validation, log warning
                actual_tp = float(take_profit[0])
                logger.warning(f"⚠️ VALIDATION: Multiple TPs detected as list, using first TP {actual_tp} for validation")
                corrected_params["take_profit"] = actual_tp
                take_profit = actual_tp

            tp_errors, corrected_tp = self._validate_take_profit(symbol_info, action, price, take_profit)
            if corrected_tp != take_profit:
                corrected_params["take_profit"] = corrected_tp
                logger.info(f"🔍 VALIDATION: Auto-corrected take profit from {take_profit} to {corrected_tp}")
            elif tp_errors:
                errors.extend(tp_errors)
        
        # Validate trading permissions
        account_info = mt5.account_info()
        if account_info and not account_info.trade_allowed:
            errors.append(ValidationError(
                error_type="TRADING_NOT_ALLOWED",
                message="Trading is not allowed for this account",
                retry_possible=False
            ))
        
        # Validate symbol trading mode
        if symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_DISABLED:
            errors.append(ValidationError(
                error_type="SYMBOL_TRADING_DISABLED",
                message=f"Trading is disabled for symbol {symbol}",
                retry_possible=False
            ))
        
        # Determine result
        if errors:
            # Check if any errors are non-retryable
            non_retryable = any(not error.retry_possible for error in errors)
            if non_retryable:
                result = ValidationResult.INVALID
            else:
                result = ValidationResult.RETRY_REQUIRED
        else:
            result = ValidationResult.VALID
        
        validation_result = TradeValidationResult(result, errors, corrected_params if corrected_params else None)
        
        logger.info(f"🔍 VALIDATION: Validation result: {result.value}, Errors: {len(errors)}, Corrections: {len(corrected_params)}")
        
        return validation_result
    
    def validate_position_modification(
        self,
        ticket: int,
        new_stop_loss: Optional[float] = None,
        new_take_profit: Optional[float] = None
    ) -> TradeValidationResult:
        """Validate position modification parameters"""
        logger.info(f"🔍 VALIDATION: Validating position modification for ticket {ticket}")
        
        errors = []
        corrected_params = {}
        
        # Get position info
        position = mt5.positions_get(ticket=ticket)
        if not position:
            errors.append(ValidationError(
                error_type="POSITION_NOT_FOUND",
                message=f"Position {ticket} not found",
                retry_possible=False
            ))
            return TradeValidationResult(ValidationResult.INVALID, errors)
        
        pos = position[0]
        symbol = pos.symbol
        
        # Get symbol info
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            errors.append(ValidationError(
                error_type="SYMBOL_NOT_FOUND",
                message=f"Symbol {symbol} not found",
                retry_possible=False
            ))
            return TradeValidationResult(ValidationResult.INVALID, errors)
        
        # Normalize prices first
        if new_stop_loss is not None:
            original_sl = new_stop_loss
            new_stop_loss = self._normalize_price(symbol, new_stop_loss)
            if new_stop_loss != original_sl:
                corrected_params["stop_loss"] = new_stop_loss
                logger.info(f"🔍 VALIDATION: Stop loss normalized from {original_sl} to {new_stop_loss}")

        if new_take_profit is not None:
            original_tp = new_take_profit
            new_take_profit = self._normalize_price(symbol, new_take_profit)
            if new_take_profit != original_tp:
                corrected_params["take_profit"] = new_take_profit
                logger.info(f"🔍 VALIDATION: Take profit normalized from {original_tp} to {new_take_profit}")

        # Validate stop loss modification
        if new_stop_loss is not None:
            action = "BUY" if pos.type == mt5.ORDER_TYPE_BUY else "SELL"
            sl_errors, corrected_sl = self._validate_stop_loss(symbol_info, action, pos.price_open, new_stop_loss)
            if corrected_sl != new_stop_loss:
                corrected_params["stop_loss"] = corrected_sl
                logger.info(f"🔍 VALIDATION: Auto-corrected stop loss from {new_stop_loss} to {corrected_sl}")
            elif sl_errors:
                errors.extend(sl_errors)

        # Validate take profit modification
        if new_take_profit is not None:
            action = "BUY" if pos.type == mt5.ORDER_TYPE_BUY else "SELL"
            tp_errors, corrected_tp = self._validate_take_profit(symbol_info, action, pos.price_open, new_take_profit)
            if corrected_tp != new_take_profit:
                corrected_params["take_profit"] = corrected_tp
                logger.info(f"🔍 VALIDATION: Auto-corrected take profit from {new_take_profit} to {corrected_tp}")
            elif tp_errors:
                errors.extend(tp_errors)
        
        # Determine result
        if errors:
            non_retryable = any(not error.retry_possible for error in errors)
            result = ValidationResult.INVALID if non_retryable else ValidationResult.RETRY_REQUIRED
        else:
            result = ValidationResult.VALID
        
        validation_result = TradeValidationResult(result, errors, corrected_params if corrected_params else None)
        
        logger.info(f"🔍 VALIDATION: Modification validation result: {result.value}, Errors: {len(errors)}")
        
        return validation_result
    
    def _validate_volume(self, symbol_info, volume: float) -> List[ValidationError]:
        """Validate volume parameters"""
        errors = []
        
        if volume <= 0:
            errors.append(ValidationError(
                error_type="INVALID_VOLUME",
                message="Volume must be greater than 0",
                retry_possible=False
            ))
        
        if volume < symbol_info.volume_min:
            errors.append(ValidationError(
                error_type="VOLUME_TOO_SMALL",
                message=f"Volume {volume} below minimum {symbol_info.volume_min}",
                suggested_fix=f"Use minimum volume {symbol_info.volume_min}",
                retry_possible=True
            ))
        
        if volume > symbol_info.volume_max:
            errors.append(ValidationError(
                error_type="VOLUME_TOO_LARGE",
                message=f"Volume {volume} above maximum {symbol_info.volume_max}",
                suggested_fix=f"Use maximum volume {symbol_info.volume_max}",
                retry_possible=True
            ))
        
        return errors
    
    def _correct_volume(self, symbol_info, volume: float) -> float:
        """Correct volume to valid range"""
        if volume < symbol_info.volume_min:
            return symbol_info.volume_min
        elif volume > symbol_info.volume_max:
            return symbol_info.volume_max
        return volume

    def _normalize_price(self, symbol: str, price: float) -> float:
        """Normalize price to the correct number of decimal places for the symbol"""
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info and symbol_info.digits > 0:
                # Round to the symbol's digit precision
                normalized_price = round(price, symbol_info.digits)
                logger.debug(f"🔍 VALIDATION_NORMALIZE: {symbol} - Original: {price}, Normalized: {normalized_price}, Digits: {symbol_info.digits}")
                return normalized_price
            return round(price, 5)  # Default to 5 decimal places for forex
        except Exception as e:
            logger.warning(f"⚠️ VALIDATION_NORMALIZE: Error normalizing price for {symbol}: {e}, using original price: {price}")
            return round(price, 5)

    def _validate_stop_loss(self, symbol_info, action: str, price: float, stop_loss: float) -> Tuple[List[ValidationError], float]:
        """Validate and correct stop loss"""
        errors = []
        corrected_sl = stop_loss
        
        # Calculate minimum distance
        min_distance = symbol_info.trade_stops_level * symbol_info.point
        pip_size = self._get_pip_size(symbol_info.name)
        min_distance_pips = self.min_sl_distance_pips * pip_size
        
        # Use the larger of the two minimum distances
        required_distance = max(min_distance, min_distance_pips)
        
        # Check distance based on action
        if action.upper() == "BUY":
            # For BUY, stop loss should be below entry price
            if stop_loss >= price:
                errors.append(ValidationError(
                    error_type="INVALID_SL_DIRECTION",
                    message=f"Stop loss {stop_loss} must be below entry price {price} for BUY order",
                    retry_possible=True
                ))
                corrected_sl = price - required_distance
            elif (price - stop_loss) < required_distance:
                errors.append(ValidationError(
                    error_type="SL_TOO_CLOSE",
                    message=f"Stop loss too close to entry price. Distance: {price - stop_loss}, Required: {required_distance}",
                    retry_possible=True
                ))
                corrected_sl = price - required_distance
        else:  # SELL
            # For SELL, stop loss should be above entry price
            if stop_loss <= price:
                errors.append(ValidationError(
                    error_type="INVALID_SL_DIRECTION",
                    message=f"Stop loss {stop_loss} must be above entry price {price} for SELL order",
                    retry_possible=True
                ))
                corrected_sl = price + required_distance
            elif (stop_loss - price) < required_distance:
                errors.append(ValidationError(
                    error_type="SL_TOO_CLOSE",
                    message=f"Stop loss too close to entry price. Distance: {stop_loss - price}, Required: {required_distance}",
                    retry_possible=True
                ))
                corrected_sl = price + required_distance
        
        return errors, corrected_sl
    
    def _validate_take_profit(self, symbol_info, action: str, price: float, take_profit: float) -> Tuple[List[ValidationError], float]:
        """Validate and correct take profit"""
        errors = []
        corrected_tp = take_profit
        
        # Calculate minimum distance
        min_distance = symbol_info.trade_stops_level * symbol_info.point
        pip_size = self._get_pip_size(symbol_info.name)
        min_distance_pips = self.min_tp_distance_pips * pip_size
        
        # Use the larger of the two minimum distances
        required_distance = max(min_distance, min_distance_pips)
        
        # Check distance based on action
        if action.upper() == "BUY":
            # For BUY, take profit should be above entry price
            if take_profit <= price:
                errors.append(ValidationError(
                    error_type="INVALID_TP_DIRECTION",
                    message=f"Take profit {take_profit} must be above entry price {price} for BUY order",
                    retry_possible=True
                ))
                corrected_tp = price + required_distance
            elif (take_profit - price) < required_distance:
                errors.append(ValidationError(
                    error_type="TP_TOO_CLOSE",
                    message=f"Take profit too close to entry price. Distance: {take_profit - price}, Required: {required_distance}",
                    retry_possible=True
                ))
                corrected_tp = price + required_distance
        else:  # SELL
            # For SELL, take profit should be below entry price
            if take_profit >= price:
                errors.append(ValidationError(
                    error_type="INVALID_TP_DIRECTION",
                    message=f"Take profit {take_profit} must be below entry price {price} for SELL order",
                    retry_possible=True
                ))
                corrected_tp = price - required_distance
            elif (price - take_profit) < required_distance:
                errors.append(ValidationError(
                    error_type="TP_TOO_CLOSE",
                    message=f"Take profit too close to entry price. Distance: {price - take_profit}, Required: {required_distance}",
                    retry_possible=True
                ))
                corrected_tp = price - required_distance
        
        return errors, corrected_tp
    
    def _get_pip_size(self, symbol: str) -> float:
        """Get pip size for symbol"""
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            return 0.0001  # Default for major pairs

        # For most forex pairs, pip is 0.0001, except JPY pairs where it's 0.01
        if 'JPY' in symbol:
            return 0.01
        else:
            return 0.0001
