{"summary": {"total_tests": 27, "passed": 4, "failed": 3, "errors": 20, "skipped": 0, "success_rate": 14.***************, "total_duration": 0.372317, "start_time": "2025-08-01T13:32:42.347921", "end_time": "2025-08-01T13:32:42.720238"}, "status": "NOT_READY", "test_details": [{"test_name": "test_load_accounts_comprehensive (test_comprehensive_trading_system.TestAccountManagement)", "status": "FAIL", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 112, in test_load_accounts_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "timestamp": "2025-08-01T13:32:42.414965"}, {"test_name": "test_money_management_factory_all_types (test_comprehensive_trading_system.TestAccountManagement)", "status": "ERROR", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 162, in test_money_management_factory_all_types\n    mm_strategy = self.account_manager.money_management_factory.create_strategy(mm_type)\nTypeError: MoneyManagementFactory.create_strategy() missing 1 required positional argument: 'config'\n", "timestamp": "2025-08-01T13:32:42.414965"}, {"test_name": "test_strategy_factory_all_types (test_comprehensive_trading_system.TestAccountManagement)", "status": "ERROR", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 153, in test_strategy_factory_all_types\n    strategy = self.account_manager.strategy_factory.create_strategy(strategy_type)\nTypeError: StrategyFactory.create_strategy() missing 1 required positional argument: 'config'\n", "timestamp": "2025-08-01T13:32:42.414965"}, {"test_name": "Account Management Tests_test_0", "status": "PASS", "duration": 0.*****************, "error_msg": null, "timestamp": "2025-08-01T13:32:42.414965"}, {"test_name": "test_ai_prompts_all_strategies (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0021190643310546875, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.426842"}, {"test_name": "test_anti_martingale_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0021190643310546875, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.426842"}, {"test_name": "test_fixed_volume_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0021190643310546875, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.426842"}, {"test_name": "test_martingale_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0021190643310546875, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.426842"}, {"test_name": "test_percent_risk_strategy_comprehensive (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0021190643310546875, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.426842"}, {"test_name": "test_trend_following_strategy (test_comprehensive_trading_system.TestTradingStrategies)", "status": "ERROR", "duration": 0.0013675689697265625, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 361, in test_trend_following_strategy\n    valid_signal = TradingSignal(\nTypeError: TradingSignal.__init__() missing 1 required positional argument: 'risk_level'\n", "timestamp": "2025-08-01T13:32:42.431942"}, {"test_name": "Trading Strategy Tests_test_0", "status": "PASS", "duration": 0.0013675689697265625, "error_msg": null, "timestamp": "2025-08-01T13:32:42.431942"}, {"test_name": "Trading Strategy Tests_test_1", "status": "PASS", "duration": 0.0013675689697265625, "error_msg": null, "timestamp": "2025-08-01T13:32:42.431942"}, {"test_name": "test_prompt_builder_comprehensive (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0033799171447753905, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.449846"}, {"test_name": "test_prompt_builder_error_handling (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0033799171447753905, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.449846"}, {"test_name": "test_qwen_client_api_call (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0033799171447753905, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.449846"}, {"test_name": "test_qwen_client_error_handling (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0033799171447753905, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.449846"}, {"test_name": "test_qwen_client_initialization (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0033799171447753905, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:32:42.449846"}, {"test_name": "test_market_data_retrieval (test_comprehensive_trading_system.TestMT5Integration)", "status": "FAIL", "duration": 0.006933132807413737, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 584, in test_market_data_retrieval\n    self.assertIsNotNone(market_data)\nAssertionError: unexpectedly None\n", "timestamp": "2025-08-01T13:32:42.471646"}, {"test_name": "test_mt5_login_comprehensive (test_comprehensive_trading_system.TestMT5Integration)", "status": "FAIL", "duration": 0.006933132807413737, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 561, in test_mt5_login_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "timestamp": "2025-08-01T13:32:42.471646"}, {"test_name": "MT5 Integration Tests_test_0", "status": "PASS", "duration": 0.006933132807413737, "error_msg": null, "timestamp": "2025-08-01T13:32:42.471646"}, {"test_name": "test_closed_trade_retrieval (test_real_world_integration.TestRealWorldSignalExecution)", "status": "ERROR", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1393, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 135, in __enter__\n    return next(self.gen)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1358, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 492, in enter_context\n    result = _cm_type.__enter__(cm)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "timestamp": "2025-08-01T13:32:42.720238"}, {"test_name": "test_complete_signal_lifecycle (test_real_world_integration.TestRealWorldSignalExecution)", "status": "ERROR", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1393, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 135, in __enter__\n    return next(self.gen)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1358, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 492, in enter_context\n    result = _cm_type.__enter__(cm)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "timestamp": "2025-08-01T13:32:42.720238"}, {"test_name": "test_error_recovery_scenarios (test_real_world_integration.TestRealWorldSignalExecution)", "status": "ERROR", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 413, in test_error_recovery_scenarios\n    with patch('src.mt5_integration.mt5_client.mt5') as mock_mt5:\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "timestamp": "2025-08-01T13:32:42.720238"}, {"test_name": "test_magic_number_tracking (test_real_world_integration.TestRealWorldSignalExecution)", "status": "ERROR", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 83, in _callMaybeAsync\n    ret = func(*args, **kwargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 399, in test_magic_number_tracking\n    strategy = self.account_manager.strategy_factory.create_strategy(strategy_type)\nTypeError: StrategyFactory.create_strategy() missing 1 required positional argument: 'config'\n", "timestamp": "2025-08-01T13:32:42.720238"}, {"test_name": "test_multiple_take_profit_execution (test_real_world_integration.TestRealWorldSignalExecution)", "status": "ERROR", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1393, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 135, in __enter__\n    return next(self.gen)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1358, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 492, in enter_context\n    result = _cm_type.__enter__(cm)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "timestamp": "2025-08-01T13:32:42.720238"}, {"test_name": "test_pending_order_management (test_real_world_integration.TestRealWorldSignalExecution)", "status": "ERROR", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1393, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 135, in __enter__\n    return next(self.gen)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1358, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 492, in enter_context\n    result = _cm_type.__enter__(cm)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "timestamp": "2025-08-01T13:32:42.720238"}, {"test_name": "test_trade_lifecycle_with_ai_management (test_real_world_integration.TestRealWorldSignalExecution)", "status": "ERROR", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 329, in test_trade_lifecycle_with_ai_management\n    with patch('src.mt5_integration.mt5_client.mt5') as mock_mt5:\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "timestamp": "2025-08-01T13:32:42.720238"}], "failures": ["test_load_accounts_comprehensive (test_comprehensive_trading_system.TestAccountManagement): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 112, in test_load_accounts_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "test_market_data_retrieval (test_comprehensive_trading_system.TestMT5Integration): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 584, in test_market_data_retrieval\n    self.assertIsNotNone(market_data)\nAssertionError: unexpectedly None\n", "test_mt5_login_comprehensive (test_comprehensive_trading_system.TestMT5Integration): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 561, in test_mt5_login_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n"], "errors": ["test_money_management_factory_all_types (test_comprehensive_trading_system.TestAccountManagement): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 162, in test_money_management_factory_all_types\n    mm_strategy = self.account_manager.money_management_factory.create_strategy(mm_type)\nTypeError: MoneyManagementFactory.create_strategy() missing 1 required positional argument: 'config'\n", "test_strategy_factory_all_types (test_comprehensive_trading_system.TestAccountManagement): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 153, in test_strategy_factory_all_types\n    strategy = self.account_manager.strategy_factory.create_strategy(strategy_type)\nTypeError: StrategyFactory.create_strategy() missing 1 required positional argument: 'config'\n", "test_ai_prompts_all_strategies (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_anti_martingale_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_fixed_volume_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_martingale_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_percent_risk_strategy_comprehensive (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_trend_following_strategy (test_comprehensive_trading_system.TestTradingStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 361, in test_trend_following_strategy\n    valid_signal = TradingSignal(\nTypeError: TradingSignal.__init__() missing 1 required positional argument: 'risk_level'\n", "test_prompt_builder_comprehensive (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_prompt_builder_error_handling (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_qwen_client_api_call (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_qwen_client_error_handling (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_qwen_client_initialization (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_closed_trade_retrieval (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1393, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 135, in __enter__\n    return next(self.gen)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1358, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 492, in enter_context\n    result = _cm_type.__enter__(cm)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "test_complete_signal_lifecycle (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1393, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 135, in __enter__\n    return next(self.gen)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1358, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 492, in enter_context\n    result = _cm_type.__enter__(cm)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "test_error_recovery_scenarios (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 413, in test_error_recovery_scenarios\n    with patch('src.mt5_integration.mt5_client.mt5') as mock_mt5:\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "test_magic_number_tracking (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 83, in _callMaybeAsync\n    ret = func(*args, **kwargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 399, in test_magic_number_tracking\n    strategy = self.account_manager.strategy_factory.create_strategy(strategy_type)\nTypeError: StrategyFactory.create_strategy() missing 1 required positional argument: 'config'\n", "test_multiple_take_profit_execution (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1393, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 135, in __enter__\n    return next(self.gen)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1358, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 492, in enter_context\n    result = _cm_type.__enter__(cm)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "test_pending_order_management (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1393, in patched\n    with self.decoration_helper(patched,\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 135, in __enter__\n    return next(self.gen)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1358, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py\", line 492, in enter_context\n    result = _cm_type.__enter__(cm)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n", "test_trade_lifecycle_with_ai_management (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 329, in test_trade_lifecycle_with_ai_management\n    with patch('src.mt5_integration.mt5_client.mt5') as mock_mt5:\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1431, in __enter__\n    self.target = self.getter()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1618, in <lambda>\n    getter = lambda: _importer(target)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1257, in _importer\n    thing = __import__(import_path)\nModuleNotFoundError: No module named 'src'\n"], "recommendations": ["🚨 DO NOT deploy to production with current issues", "🚨 Fix all critical errors before proceeding", "🚨 Re-run full test suite after fixes", "📝 Consider code review for failing components", "📝 Test on demo accounts extensively before retry", "📊 Current success rate: 14.8% - Target: 100%"]}