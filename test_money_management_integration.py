#!/usr/bin/env python3
"""
Money Management Integration Testing
Tests all money management strategies with AI decisions and validates calculations
"""

import sys
import os
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List

# Add src to path
sys.path.append('src')

from money_management.percent_risk import PercentRiskStrategy
from money_management.fixed_volume import FixedVolumeStrategy
from money_management.martingale import MartingaleStrategy
from money_management.anti_martingale import AntiMartingaleStrategy
from money_management.base_strategy import AccountInfo, TradeParameters
from ai_integration.qwen_client import QwenClient
from ai_integration.prompt_builder import PromptBuilder

class MoneyManagementValidator:
    """Validates money management integration with AI decisions"""
    
    def __init__(self):
        self.test_results = []
        self.prompt_builder = PromptBuilder()
        
    async def run_comprehensive_validation(self):
        """Run comprehensive money management validation"""
        print("💰 MONEY MANAGEMENT INTEGRATION VALIDATION")
        print("=" * 60)
        
        # Test all money management strategies
        await self._test_percent_risk_strategy()
        await self._test_fixed_volume_strategy()
        await self._test_martingale_strategy()
        await self._test_anti_martingale_strategy()
        
        # Test AI integration
        await self._test_ai_integration()
        
        # Analyze results
        self._analyze_results()
        
    async def _test_percent_risk_strategy(self):
        """Test percent risk strategy calculations"""
        print("\n📊 Testing Percent Risk Strategy")
        print("-" * 40)
        
        strategy = PercentRiskStrategy({'risk_percent': 2.0})
        
        account_info = AccountInfo(
            balance=74.40,
            equity=74.40,
            margin=0.0,
            free_margin=74.40,
            margin_level=0.0,
            currency="USD",
            leverage=500
        )
        
        # Test scenarios with different stop loss distances
        test_scenarios = [
            {
                "symbol": "EURUSD",
                "entry_price": 1.0850,
                "stop_loss": 1.0820,  # 30 pips
                "expected_risk": 1.488,  # 2% of 74.40
                "description": "30 pip stop loss"
            },
            {
                "symbol": "EURUSD", 
                "entry_price": 1.0850,
                "stop_loss": 1.0830,  # 20 pips
                "expected_risk": 1.488,
                "description": "20 pip stop loss"
            },
            {
                "symbol": "EURUSD",
                "entry_price": 1.0850,
                "stop_loss": None,  # No stop loss
                "expected_risk": 1.488,
                "description": "No stop loss (should use default)"
            }
        ]
        
        market_data = {
            'pip_value': 10.0,  # $10 per pip for 1 lot EURUSD
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }
        
        for scenario in test_scenarios:
            print(f"  {scenario['description']}:")
            
            trade_params = strategy.calculate_position_size(
                account_info=account_info,
                symbol=scenario['symbol'],
                entry_price=scenario['entry_price'],
                stop_loss=scenario['stop_loss'],
                trade_history=[],
                market_data=market_data
            )
            
            print(f"    Volume: {trade_params.volume:.2f}")
            print(f"    Risk Amount: ${trade_params.risk_amount:.2f}")
            print(f"    Expected Risk: ${scenario['expected_risk']:.2f}")
            
            # Validate calculations
            if scenario['stop_loss']:
                pip_distance = abs(scenario['entry_price'] - scenario['stop_loss']) / market_data['pip_size']
                expected_volume = scenario['expected_risk'] / (pip_distance * market_data['pip_value'])
                expected_volume = max(0.01, min(expected_volume, 100.0))  # Apply limits
                
                volume_correct = abs(trade_params.volume - expected_volume) < 0.01
                risk_reasonable = trade_params.risk_amount <= scenario['expected_risk'] * 1.1  # Allow 10% tolerance
                
                print(f"    Pip Distance: {pip_distance:.1f}")
                print(f"    Expected Volume: {expected_volume:.4f}")
                print(f"    Volume Correct: {'✅' if volume_correct else '❌'}")
                print(f"    Risk Reasonable: {'✅' if risk_reasonable else '❌'}")
                
                self.test_results.append({
                    'strategy': 'percent_risk',
                    'scenario': scenario['description'],
                    'volume_correct': volume_correct,
                    'risk_reasonable': risk_reasonable,
                    'passed': volume_correct and risk_reasonable
                })
            else:
                # No stop loss case
                has_volume = trade_params.volume > 0
                has_risk = trade_params.risk_amount > 0
                
                print(f"    Has Volume: {'✅' if has_volume else '❌'}")
                print(f"    Has Risk: {'✅' if has_risk else '❌'}")
                
                self.test_results.append({
                    'strategy': 'percent_risk',
                    'scenario': scenario['description'],
                    'has_volume': has_volume,
                    'has_risk': has_risk,
                    'passed': has_volume and has_risk
                })
                
    async def _test_fixed_volume_strategy(self):
        """Test fixed volume strategy"""
        print("\n📏 Testing Fixed Volume Strategy")
        print("-" * 40)
        
        strategy = FixedVolumeStrategy({'fixed_volume': 0.01})
        
        account_info = AccountInfo(
            balance=74.40,
            equity=74.40,
            margin=0.0,
            free_margin=74.40,
            margin_level=0.0,
            currency="USD",
            leverage=500
        )
        
        market_data = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }
        
        trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=1.0850,
            stop_loss=1.0820,
            trade_history=[],
            market_data=market_data
        )
        
        print(f"  Fixed Volume: {trade_params.volume:.2f}")
        print(f"  Risk Amount: ${trade_params.risk_amount:.2f}")
        
        volume_correct = trade_params.volume == 0.01
        has_risk_calc = trade_params.risk_amount > 0
        
        print(f"  Volume Correct: {'✅' if volume_correct else '❌'}")
        print(f"  Risk Calculated: {'✅' if has_risk_calc else '❌'}")
        
        self.test_results.append({
            'strategy': 'fixed_volume',
            'volume_correct': volume_correct,
            'has_risk_calc': has_risk_calc,
            'passed': volume_correct and has_risk_calc
        })
        
    async def _test_martingale_strategy(self):
        """Test martingale strategy"""
        print("\n📈 Testing Martingale Strategy")
        print("-" * 40)
        
        strategy = MartingaleStrategy({'base_volume': 0.01, 'max_multiplier': 8})
        
        account_info = AccountInfo(
            balance=74.40,
            equity=74.40,
            margin=0.0,
            free_margin=74.40,
            margin_level=0.0,
            currency="USD",
            leverage=500
        )
        
        market_data = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }
        
        # Test with different loss streaks
        loss_scenarios = [
            {"losses": 0, "expected_volume": 0.01, "description": "No losses"},
            {"losses": 1, "expected_volume": 0.02, "description": "1 loss"},
            {"losses": 2, "expected_volume": 0.04, "description": "2 losses"},
            {"losses": 3, "expected_volume": 0.08, "description": "3 losses"}
        ]
        
        for scenario in loss_scenarios:
            # Create mock trade history
            trade_history = []
            for i in range(scenario['losses']):
                trade_history.append({
                    'symbol': 'EURUSD',
                    'profit': -10.0,  # Loss
                    'close_time': datetime.now()
                })
            
            trade_params = strategy.calculate_position_size(
                account_info=account_info,
                symbol="EURUSD",
                entry_price=1.0850,
                stop_loss=1.0820,
                trade_history=trade_history,
                market_data=market_data
            )
            
            print(f"  {scenario['description']}:")
            print(f"    Volume: {trade_params.volume:.2f}")
            print(f"    Expected: {scenario['expected_volume']:.2f}")
            
            volume_correct = abs(trade_params.volume - scenario['expected_volume']) < 0.001
            print(f"    Correct: {'✅' if volume_correct else '❌'}")
            
            self.test_results.append({
                'strategy': 'martingale',
                'scenario': scenario['description'],
                'volume_correct': volume_correct,
                'passed': volume_correct
            })
            
    async def _test_anti_martingale_strategy(self):
        """Test anti-martingale strategy"""
        print("\n📉 Testing Anti-Martingale Strategy")
        print("-" * 40)
        
        strategy = AntiMartingaleStrategy({'base_volume': 0.01, 'max_multiplier': 4})
        
        account_info = AccountInfo(
            balance=74.40,
            equity=74.40,
            margin=0.0,
            free_margin=74.40,
            margin_level=0.0,
            currency="USD",
            leverage=500
        )
        
        market_data = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }
        
        # Test with different win streaks
        win_scenarios = [
            {"wins": 0, "expected_volume": 0.01, "description": "No wins"},
            {"wins": 1, "expected_volume": 0.015, "description": "1 win"},
            {"wins": 2, "expected_volume": 0.02, "description": "2 wins"}
        ]
        
        for scenario in win_scenarios:
            # Create mock trade history
            trade_history = []
            for i in range(scenario['wins']):
                trade_history.append({
                    'symbol': 'EURUSD',
                    'profit': 10.0,  # Win
                    'close_time': datetime.now()
                })
            
            trade_params = strategy.calculate_position_size(
                account_info=account_info,
                symbol="EURUSD",
                entry_price=1.0850,
                stop_loss=1.0820,
                trade_history=trade_history,
                market_data=market_data
            )
            
            print(f"  {scenario['description']}:")
            print(f"    Volume: {trade_params.volume:.3f}")
            print(f"    Expected: {scenario['expected_volume']:.3f}")
            
            volume_correct = abs(trade_params.volume - scenario['expected_volume']) < 0.001
            print(f"    Correct: {'✅' if volume_correct else '❌'}")
            
            self.test_results.append({
                'strategy': 'anti_martingale',
                'scenario': scenario['description'],
                'volume_correct': volume_correct,
                'passed': volume_correct
            })
            
    async def _test_ai_integration(self):
        """Test AI integration with money management"""
        print("\n🤖 Testing AI Integration")
        print("-" * 40)
        
        # Test that AI prompts include money management context
        strategy = PercentRiskStrategy({'risk_percent': 2.0})
        
        account_info = AccountInfo(
            balance=74.40,
            equity=74.40,
            margin=0.0,
            free_margin=74.40,
            margin_level=0.0,
            currency="USD",
            leverage=500
        )
        
        ai_prompt = strategy.get_ai_prompt(account_info, [])
        
        # Check if prompt contains key money management concepts
        required_concepts = [
            'percent risk',
            'risk amount',
            'position size',
            'stop loss',
            'balance'
        ]
        
        concepts_found = []
        for concept in required_concepts:
            if concept.lower() in ai_prompt.lower():
                concepts_found.append(concept)
        
        print(f"  AI Prompt Length: {len(ai_prompt)} characters")
        print(f"  Concepts Found: {len(concepts_found)}/{len(required_concepts)}")
        
        for concept in required_concepts:
            found = concept in concepts_found
            print(f"    {concept}: {'✅' if found else '❌'}")
        
        integration_complete = len(concepts_found) >= len(required_concepts) * 0.8
        
        self.test_results.append({
            'strategy': 'ai_integration',
            'concepts_found': len(concepts_found),
            'total_concepts': len(required_concepts),
            'passed': integration_complete
        })
        
    def _analyze_results(self):
        """Analyze money management test results"""
        print("\n🔍 MONEY MANAGEMENT ANALYSIS")
        print("=" * 60)
        
        # Group results by strategy
        strategy_groups = {}
        for result in self.test_results:
            strategy = result['strategy']
            if strategy not in strategy_groups:
                strategy_groups[strategy] = []
            strategy_groups[strategy].append(result)
        
        overall_pass = True
        
        for strategy, results in strategy_groups.items():
            print(f"\n📈 {strategy.replace('_', ' ').title()}:")
            
            passed_tests = [r for r in results if r.get('passed', False)]
            total_tests = len(results)
            pass_rate = len(passed_tests) / total_tests if total_tests > 0 else 0
            
            print(f"  Passed: {len(passed_tests)}/{total_tests} ({pass_rate:.1%})")
            
            if pass_rate < 1.0:
                overall_pass = False
                print(f"    ❌ Some tests failed")
            else:
                print(f"    ✅ All tests passed")
        
        print(f"\n🎯 OVERALL RESULT: {'✅ PASS' if overall_pass else '❌ FAIL'}")
        return overall_pass

async def main():
    """Main test function"""
    validator = MoneyManagementValidator()
    
    try:
        await validator.run_comprehensive_validation()
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(main())
