"""
Fixed Volume Money Management Strategy
"""

from typing import Dict, Any, Optional
from money_management.base_strategy import BaseMoneyManagement, MoneyManagementType, TradeParameters, AccountInfo

class FixedVolumeStrategy(BaseMoneyManagement):
    """
    Fixed Volume Money Management Strategy
    Uses a predetermined fixed lot size for all trades
    """
    
    def get_strategy_type(self) -> MoneyManagementType:
        return MoneyManagementType.FIXED_VOLUME
    
    def calculate_position_size(
        self, 
        account_info: AccountInfo,
        symbol: str,
        entry_price: float,
        stop_loss: Optional[float],
        trade_history: list,
        market_data: Dict[str, Any]
    ) -> TradeParameters:
        """Calculate fixed position size"""
        
        # Get fixed volume from config
        fixed_volume = self.config.get('fixed_volume', 0.01)

        # Validate volume against market constraints
        min_volume = market_data.get('min_volume', 0.01)
        max_volume = market_data.get('max_volume', 100.0)

        if fixed_volume < min_volume:
            #logger.warning(f"Fixed volume {fixed_volume} below minimum {min_volume}, using minimum")
            fixed_volume = min_volume
        elif fixed_volume > max_volume:
            #logger.warning(f"Fixed volume {fixed_volume} above maximum {max_volume}, using maximum")
            fixed_volume = max_volume

        # Calculate risk amount if stop loss is provided
        risk_amount = 0
        max_loss = 0
        
        if stop_loss:
            pip_value = market_data.get('pip_value', 1.0)
            pip_difference = abs(entry_price - stop_loss) / market_data.get('pip_size', 0.0001)
            risk_amount = fixed_volume * pip_difference * pip_value
            max_loss = risk_amount
        
        return TradeParameters(
            volume=fixed_volume,
            stop_loss=stop_loss,
            take_profit=None,  # Will be determined by AI
            risk_amount=risk_amount,
            max_loss=max_loss,
            confidence_level=0.5  # Neutral confidence for fixed volume
        )
    
    def get_ai_prompt(self, account_info: AccountInfo, trade_history: list) -> str:
        """Get AI prompt for fixed volume strategy"""
        
        recent_trades = trade_history[-10:] if len(trade_history) > 10 else trade_history
        win_rate = self._calculate_win_rate(recent_trades)
        avg_profit = self._calculate_average_profit(recent_trades)
        
        prompt = f"""
You are managing a trading account using a FIXED VOLUME money management strategy.

ACCOUNT INFORMATION:
- Balance: ${account_info.balance:,.2f}
- Equity: ${account_info.equity:,.2f}
- Free Margin: ${account_info.free_margin:,.2f}
- Currency: {account_info.currency}
- Leverage: 1:{account_info.leverage}

MONEY MANAGEMENT STRATEGY: Fixed Volume
- Fixed lot size: {self.config.get('fixed_volume', 0.01)} lots per trade
- This strategy maintains consistent position sizing regardless of market conditions
- Risk per trade is variable depending on stop loss distance

RECENT PERFORMANCE:
- Win Rate: {win_rate:.1f}%
- Average Profit per Trade: ${avg_profit:.2f}
- Total Recent Trades: {len(recent_trades)}

STRATEGY GUIDELINES:
1. Use the fixed volume of {self.config.get('fixed_volume', 0.01)} lots for all trades
2. Focus on high-probability setups since position size is constant
3. Adjust stop loss and take profit levels to optimize risk-reward ratio
4. Consider market volatility when setting stop losses
5. Avoid overtrading - quality over quantity with fixed volume

RISK MANAGEMENT RULES:
- Maximum daily trades: {self.config.get('max_daily_trades', 5)}
- Never risk more than 5% of account balance per trade
- Maintain proper stop losses on all positions
- Consider correlation between open positions

Based on the market data and recent performance, provide your trading decisions with:
1. Entry signals with specific entry prices
2. Stop loss levels (crucial for risk calculation)
3. Take profit targets
4. Trade management instructions
5. Overall market assessment and strategy adjustments

Remember: With fixed volume, your edge comes from timing and precision, not position sizing.
"""
        return prompt
    
    def _calculate_win_rate(self, trades: list) -> float:
        """Calculate win rate from trade history"""
        if not trades:
            return 0.0
        
        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        return (winning_trades / len(trades)) * 100
    
    def _calculate_average_profit(self, trades: list) -> float:
        """Calculate average profit from trade history"""
        if not trades:
            return 0.0
        
        total_profit = sum(trade.get('profit', 0) for trade in trades)
        return total_profit / len(trades)
