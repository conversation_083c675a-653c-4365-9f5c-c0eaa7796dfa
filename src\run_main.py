
import asyncio
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from dotenv import load_dotenv
from logging_system.logger import setup_logger, get_logger

async def main():
    """Main trading system entry point"""
    load_dotenv()
    
    logger = setup_logger()
    logger.info("AI-Driven Trading System Starting...")
    
    try:
        # Import components
        from account_management.account_manager import AccountManager
        from signal_generation.signal_generator import SignalGenerator  
        from trade_management.trade_manager import TradeManager
        
        logger.info("All components imported successfully")

        # Initialize account manager
        account_manager = AccountManager()
        if not account_manager.load_accounts():
            logger.error("Failed to load accounts")
            return

        logger.info(f"Loaded {len(account_manager.accounts)} accounts")

        # Initialize signal generator and trade manager
        signal_generator = SignalGenerator(account_manager)
        trade_manager = TradeManager(account_manager)

        logger.info("Components initialized")
        logger.info("Starting trading operations...")
        
        # Start the system
        await asyncio.gather(
            signal_generator.start(),
            trade_manager.start()
        )
        
    except KeyboardInterrupt:
        logger.info("System shutdown requested")
    except Exception as e:
        logger.error(f"System error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
