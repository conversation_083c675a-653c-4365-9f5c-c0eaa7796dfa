#!/usr/bin/env python3
"""
Simple AI Stop Loss Validation Test
Tests AI stop loss decisions without full system dependencies
"""

import sys
import os
import asyncio
import json
import re
from datetime import datetime

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_prompt_hardcoded_values():
    """Test that prompts don't contain hardcoded pip values"""
    print("🔍 TESTING PROMPT FOR HARDCODED VALUES")
    print("=" * 50)
    
    # Read the prompt builder file
    prompt_file = os.path.join('src', 'ai_integration', 'prompt_builder.py')
    
    if not os.path.exists(prompt_file):
        print("❌ Prompt builder file not found")
        return False
    
    with open(prompt_file, 'r') as f:
        content = f.read()
    
    # Check for hardcoded pip values
    hardcoded_patterns = [
        r'\d+-\d+\s+pips',  # "10-50 pips"
        r'\d+\s+pip',       # "20 pip"
        r'pip.*=.*\d+',     # "pip = 10"
        r'stop.*\d+',       # "stop 20"
    ]
    
    issues_found = []
    
    for pattern in hardcoded_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            issues_found.extend(matches)
    
    print(f"Hardcoded patterns found: {len(issues_found)}")
    for issue in issues_found:
        print(f"  ❌ Found: '{issue}'")
    
    if not issues_found:
        print("✅ No hardcoded pip values found in prompts")
        return True
    else:
        print("❌ Hardcoded values found - AI decisions may be biased")
        return False

def test_money_management_calculations():
    """Test money management calculations"""
    print("\n💰 TESTING MONEY MANAGEMENT CALCULATIONS")
    print("=" * 50)
    
    # Test percent risk calculation
    balance = 74.40
    risk_percent = 2.0
    expected_risk = balance * (risk_percent / 100)
    
    print(f"Account Balance: ${balance}")
    print(f"Risk Percent: {risk_percent}%")
    print(f"Expected Risk Amount: ${expected_risk:.2f}")
    
    # Test position sizing calculation
    test_scenarios = [
        {
            "entry_price": 1.0850,
            "stop_loss": 1.0820,
            "pip_size": 0.0001,
            "pip_value": 10.0,
            "description": "EURUSD 30 pip stop"
        },
        {
            "entry_price": 1.0850,
            "stop_loss": 1.0830,
            "pip_size": 0.0001,
            "pip_value": 10.0,
            "description": "EURUSD 20 pip stop"
        },
        {
            "entry_price": 149.85,
            "stop_loss": 149.65,
            "pip_size": 0.01,
            "pip_value": 6.67,  # Approximate for USDJPY
            "description": "USDJPY 20 pip stop"
        }
    ]
    
    all_passed = True
    
    for scenario in test_scenarios:
        print(f"\n📊 {scenario['description']}:")
        
        # Calculate pip distance
        pip_distance = abs(scenario['entry_price'] - scenario['stop_loss']) / scenario['pip_size']
        
        # Calculate required volume for target risk
        required_volume = expected_risk / (pip_distance * scenario['pip_value'])
        
        # Apply minimum volume constraint
        min_volume = 0.01
        actual_volume = max(required_volume, min_volume)
        
        # Calculate actual risk with volume constraints
        actual_risk = pip_distance * scenario['pip_value'] * actual_volume
        
        print(f"  Entry: {scenario['entry_price']:.5f}")
        print(f"  Stop Loss: {scenario['stop_loss']:.5f}")
        print(f"  Pip Distance: {pip_distance:.1f}")
        print(f"  Required Volume: {required_volume:.4f}")
        print(f"  Actual Volume: {actual_volume:.2f}")
        print(f"  Actual Risk: ${actual_risk:.2f}")
        
        # Validate calculations
        volume_reasonable = actual_volume >= min_volume
        risk_controlled = actual_risk <= expected_risk * 10  # Allow up to 10x for small accounts
        
        scenario_passed = volume_reasonable and risk_controlled
        
        print(f"  Volume >= Min: {'✅' if volume_reasonable else '❌'}")
        print(f"  Risk Controlled: {'✅' if risk_controlled else '❌'}")
        print(f"  Result: {'✅ PASS' if scenario_passed else '❌ FAIL'}")
        
        if not scenario_passed:
            all_passed = False
    
    return all_passed

def test_risk_management_settings():
    """Test risk management settings from accounts.json"""
    print("\n🛡️ TESTING RISK MANAGEMENT SETTINGS")
    print("=" * 50)
    
    accounts_file = os.path.join('config', 'accounts.json')
    
    if not os.path.exists(accounts_file):
        print("❌ accounts.json not found")
        return False
    
    try:
        with open(accounts_file, 'r') as f:
            accounts_data = json.load(f)
        
        accounts = accounts_data.get('accounts', [])
        
        if not accounts:
            print("❌ No accounts found in configuration")
            return False
        
        all_passed = True
        
        for account in accounts:
            account_id = account.get('account_id', 'Unknown')
            print(f"\n📊 Account: {account_id}")
            
            # Check required risk management settings
            mm_settings = account.get('money_management_settings', {})
            
            required_settings = [
                'risk_percent',
                'max_daily_loss',
                'max_daily_trades',
                'max_open_positions'
            ]
            
            missing_settings = []
            for setting in required_settings:
                if setting not in mm_settings:
                    missing_settings.append(setting)
                else:
                    value = mm_settings[setting]
                    print(f"  {setting}: {value}")
            
            if missing_settings:
                print(f"  ❌ Missing settings: {missing_settings}")
                all_passed = False
            else:
                print(f"  ✅ All required settings present")
                
                # Validate setting values
                risk_percent = mm_settings.get('risk_percent', 0)
                max_daily_loss = mm_settings.get('max_daily_loss', 0)
                
                if risk_percent <= 0 or risk_percent > 10:
                    print(f"  ⚠️  Risk percent {risk_percent}% may be too high/low")
                
                if max_daily_loss <= 0:
                    print(f"  ❌ Invalid max daily loss: {max_daily_loss}")
                    all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error reading accounts.json: {e}")
        return False

def test_ai_prompt_structure():
    """Test AI prompt structure for stop loss guidance"""
    print("\n🤖 TESTING AI PROMPT STRUCTURE")
    print("=" * 50)
    
    prompt_file = os.path.join('src', 'ai_integration', 'prompt_builder.py')
    
    if not os.path.exists(prompt_file):
        print("❌ Prompt builder file not found")
        return False
    
    with open(prompt_file, 'r') as f:
        content = f.read()
    
    # Check for key concepts in AI prompts
    required_concepts = [
        'stop loss',
        'risk management',
        'technical levels',
        'volatility',
        'mandatory',
        'position size'
    ]
    
    concepts_found = []
    for concept in required_concepts:
        if concept.lower() in content.lower():
            concepts_found.append(concept)
    
    print(f"Required concepts found: {len(concepts_found)}/{len(required_concepts)}")
    
    for concept in required_concepts:
        found = concept in concepts_found
        print(f"  {concept}: {'✅' if found else '❌'}")
    
    # Check for dynamic guidance (not hardcoded)
    dynamic_guidance = [
        'technical analysis',
        'market condition',
        'current.*volatility',
        'timeframe'
    ]
    
    dynamic_found = []
    for guidance in dynamic_guidance:
        if re.search(guidance, content, re.IGNORECASE):
            dynamic_found.append(guidance)
    
    print(f"\nDynamic guidance found: {len(dynamic_found)}/{len(dynamic_guidance)}")
    
    for guidance in dynamic_guidance:
        found = any(re.search(guidance, g, re.IGNORECASE) for g in dynamic_found)
        print(f"  {guidance}: {'✅' if found else '❌'}")
    
    concepts_pass = len(concepts_found) >= len(required_concepts) * 0.8
    dynamic_pass = len(dynamic_found) >= len(dynamic_guidance) * 0.5
    
    overall_pass = concepts_pass and dynamic_pass
    
    print(f"\nConcepts: {'✅ PASS' if concepts_pass else '❌ FAIL'}")
    print(f"Dynamic: {'✅ PASS' if dynamic_pass else '❌ FAIL'}")
    print(f"Overall: {'✅ PASS' if overall_pass else '❌ FAIL'}")
    
    return overall_pass

def main():
    """Main test function"""
    print("🤖 AI STOP LOSS VALIDATION TEST")
    print("=" * 60)
    
    tests = [
        ("Hardcoded Values", test_prompt_hardcoded_values),
        ("Money Management", test_money_management_calculations),
        ("Risk Settings", test_risk_management_settings),
        ("AI Prompt Structure", test_ai_prompt_structure)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎯 TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = [r for r in results if r[1]]
    total_tests = len(results)
    
    for test_name, passed in results:
        print(f"{test_name}: {'✅ PASS' if passed else '❌ FAIL'}")
    
    print(f"\nOverall: {len(passed_tests)}/{total_tests} tests passed")
    
    overall_success = len(passed_tests) == total_tests
    print(f"Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
