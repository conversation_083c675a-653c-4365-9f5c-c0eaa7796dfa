#!/usr/bin/env python3
"""
Production Readiness Assessment
Comprehensive validation that the trading system is ready for real account trading
"""

import sys
import os
import json
import subprocess
from datetime import datetime
from typing import Dict, Any, List

def run_all_validation_tests():
    """Run all validation tests and collect results"""
    print("🎯 PRODUCTION READINESS ASSESSMENT")
    print("=" * 60)
    print(f"Assessment Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_files = [
        {
            'name': 'AI Stop Loss Validation',
            'file': 'test_simple_ai_stop_loss.py',
            'critical': True,
            'description': 'Validates AI makes dynamic stop loss decisions'
        },
        {
            'name': 'Risk Management Compliance',
            'file': 'test_simple_risk_management.py',
            'critical': True,
            'description': 'Validates risk management enforcement'
        },
        {
            'name': 'Pip Calculation Validation',
            'file': 'test_pip_calculation_validation.py',
            'critical': False,
            'description': 'Validates pip calculations and normalization'
        },
        {
            'name': 'End-to-End Integration',
            'file': 'test_end_to_end_integration.py',
            'critical': True,
            'description': 'Validates complete system integration'
        }
    ]
    
    results = []
    
    for test in test_files:
        print(f"\n🔄 Running {test['name']}...")
        print("-" * 40)
        
        try:
            # Run the test
            result = subprocess.run(
                [sys.executable, test['file']],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            success = result.returncode == 0
            
            if success:
                print(f"✅ {test['name']}: PASSED")
            else:
                print(f"❌ {test['name']}: FAILED")
                if result.stderr:
                    print(f"   Error: {result.stderr[:200]}...")
            
            results.append({
                'name': test['name'],
                'file': test['file'],
                'critical': test['critical'],
                'description': test['description'],
                'passed': success,
                'output': result.stdout,
                'error': result.stderr
            })
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {test['name']}: TIMEOUT")
            results.append({
                'name': test['name'],
                'file': test['file'],
                'critical': test['critical'],
                'description': test['description'],
                'passed': False,
                'error': 'Test timeout'
            })
        except Exception as e:
            print(f"❌ {test['name']}: ERROR - {e}")
            results.append({
                'name': test['name'],
                'file': test['file'],
                'critical': test['critical'],
                'description': test['description'],
                'passed': False,
                'error': str(e)
            })
    
    return results

def assess_system_configuration():
    """Assess system configuration and setup"""
    print("\n🔧 SYSTEM CONFIGURATION ASSESSMENT")
    print("-" * 40)
    
    config_checks = []
    
    # Check accounts.json
    accounts_file = 'config/accounts.json'
    if os.path.exists(accounts_file):
        try:
            with open(accounts_file, 'r') as f:
                accounts_data = json.load(f)
            
            accounts = accounts_data.get('accounts', [])
            if accounts:
                account = accounts[0]
                mm_settings = account.get('money_management_settings', {})
                
                # Validate critical settings
                required_settings = ['risk_percent', 'max_daily_loss', 'max_daily_trades', 'max_open_positions']
                missing_settings = [s for s in required_settings if s not in mm_settings]
                
                if not missing_settings:
                    print("✅ Account configuration: Complete")
                    config_checks.append(('Account Configuration', True, 'All required settings present'))
                else:
                    print(f"❌ Account configuration: Missing {missing_settings}")
                    config_checks.append(('Account Configuration', False, f'Missing: {missing_settings}'))
            else:
                print("❌ Account configuration: No accounts found")
                config_checks.append(('Account Configuration', False, 'No accounts found'))
        except Exception as e:
            print(f"❌ Account configuration: Error reading file - {e}")
            config_checks.append(('Account Configuration', False, f'Error: {e}'))
    else:
        print("❌ Account configuration: File not found")
        config_checks.append(('Account Configuration', False, 'File not found'))
    
    # Check environment variables
    env_vars = ['OPENAI_API_KEY']
    missing_env = []
    
    for var in env_vars:
        if not os.getenv(var):
            missing_env.append(var)
    
    if not missing_env:
        print("✅ Environment variables: Complete")
        config_checks.append(('Environment Variables', True, 'All required variables set'))
    else:
        print(f"❌ Environment variables: Missing {missing_env}")
        config_checks.append(('Environment Variables', False, f'Missing: {missing_env}'))
    
    # Check directory structure
    required_dirs = ['src', 'config', 'logs']
    missing_dirs = [d for d in required_dirs if not os.path.exists(d)]
    
    if not missing_dirs:
        print("✅ Directory structure: Complete")
        config_checks.append(('Directory Structure', True, 'All required directories present'))
    else:
        print(f"❌ Directory structure: Missing {missing_dirs}")
        config_checks.append(('Directory Structure', False, f'Missing: {missing_dirs}'))
    
    return config_checks

def assess_risk_management():
    """Assess risk management configuration"""
    print("\n🛡️ RISK MANAGEMENT ASSESSMENT")
    print("-" * 40)
    
    risk_checks = []
    
    try:
        with open('config/accounts.json', 'r') as f:
            accounts_data = json.load(f)
        
        account = accounts_data['accounts'][0]
        mm_settings = account['money_management_settings']
        
        # Check risk percent
        risk_percent = mm_settings.get('risk_percent', 0)
        if 0.5 <= risk_percent <= 5.0:
            print(f"✅ Risk percent: {risk_percent}% (Reasonable)")
            risk_checks.append(('Risk Percent', True, f'{risk_percent}% - within safe range'))
        else:
            print(f"⚠️  Risk percent: {risk_percent}% (May be too high/low)")
            risk_checks.append(('Risk Percent', False, f'{risk_percent}% - outside recommended range'))
        
        # Check daily loss limit
        max_daily_loss = mm_settings.get('max_daily_loss', 0)
        balance = 74.40  # Current demo balance
        daily_loss_percent = (max_daily_loss / balance) * 100
        
        if daily_loss_percent <= 10:
            print(f"✅ Daily loss limit: ${max_daily_loss} ({daily_loss_percent:.1f}% of balance)")
            risk_checks.append(('Daily Loss Limit', True, f'${max_daily_loss} - reasonable limit'))
        else:
            print(f"⚠️  Daily loss limit: ${max_daily_loss} ({daily_loss_percent:.1f}% of balance) - High")
            risk_checks.append(('Daily Loss Limit', False, f'${max_daily_loss} - may be too high'))
        
        # Check position limits
        max_positions = mm_settings.get('max_open_positions', 0)
        if 1 <= max_positions <= 5:
            print(f"✅ Position limit: {max_positions} (Reasonable)")
            risk_checks.append(('Position Limit', True, f'{max_positions} positions - reasonable'))
        else:
            print(f"⚠️  Position limit: {max_positions} (May be too high)")
            risk_checks.append(('Position Limit', False, f'{max_positions} positions - review needed'))
        
    except Exception as e:
        print(f"❌ Risk management assessment failed: {e}")
        risk_checks.append(('Risk Management', False, f'Assessment failed: {e}'))
    
    return risk_checks

def generate_production_report(test_results, config_checks, risk_checks):
    """Generate comprehensive production readiness report"""
    print("\n📊 PRODUCTION READINESS REPORT")
    print("=" * 60)
    
    # Test Results Summary
    print("\n🧪 TEST RESULTS SUMMARY:")
    total_tests = len(test_results)
    passed_tests = [r for r in test_results if r['passed']]
    critical_tests = [r for r in test_results if r['critical']]
    critical_passed = [r for r in critical_tests if r['passed']]
    
    print(f"Total Tests: {len(passed_tests)}/{total_tests} passed")
    print(f"Critical Tests: {len(critical_passed)}/{len(critical_tests)} passed")
    
    for result in test_results:
        status = "✅ PASS" if result['passed'] else "❌ FAIL"
        critical = " (CRITICAL)" if result['critical'] else ""
        print(f"  {result['name']}: {status}{critical}")
    
    # Configuration Summary
    print("\n⚙️ CONFIGURATION SUMMARY:")
    config_passed = [c for c in config_checks if c[1]]
    print(f"Configuration Checks: {len(config_passed)}/{len(config_checks)} passed")
    
    for check_name, passed, details in config_checks:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {check_name}: {status} - {details}")
    
    # Risk Management Summary
    print("\n🛡️ RISK MANAGEMENT SUMMARY:")
    risk_passed = [r for r in risk_checks if r[1]]
    print(f"Risk Checks: {len(risk_passed)}/{len(risk_checks)} passed")
    
    for check_name, passed, details in risk_checks:
        status = "✅ PASS" if passed else "⚠️  REVIEW"
        print(f"  {check_name}: {status} - {details}")
    
    # Overall Assessment
    print("\n🎯 OVERALL ASSESSMENT:")
    
    critical_failures = [r for r in test_results if r['critical'] and not r['passed']]
    config_failures = [c for c in config_checks if not c[1]]
    
    if critical_failures:
        print("❌ CRITICAL TEST FAILURES DETECTED")
        print("⚠️  System is NOT ready for production trading")
        for failure in critical_failures:
            print(f"   - {failure['name']}: {failure.get('error', 'Failed')}")
        recommendation = "BLOCK_PRODUCTION"
    elif config_failures:
        print("⚠️  CONFIGURATION ISSUES DETECTED")
        print("🔧 System needs configuration fixes before production")
        for failure in config_failures:
            print(f"   - {failure[0]}: {failure[2]}")
        recommendation = "FIX_CONFIG"
    elif len(passed_tests) == total_tests:
        print("✅ ALL TESTS PASSED")
        print("🎉 System appears ready for production trading")
        recommendation = "READY_FOR_PRODUCTION"
    else:
        print("⚠️  SOME TESTS FAILED")
        print("🔧 System needs fixes before production trading")
        recommendation = "NEEDS_FIXES"
    
    # Recommendations
    print("\n📋 RECOMMENDATIONS:")
    
    if recommendation == "READY_FOR_PRODUCTION":
        print("✅ System is ready for production trading")
        print("📝 Recommended next steps:")
        print("   1. Set up OpenAI API key environment variable")
        print("   2. Start with small position sizes")
        print("   3. Monitor system closely for first few trades")
        print("   4. Keep daily loss limits conservative")
    elif recommendation == "FIX_CONFIG":
        print("🔧 Fix configuration issues before production:")
        print("   1. Set up missing environment variables")
        print("   2. Verify all account settings")
        print("   3. Re-run validation tests")
    else:
        print("❌ System requires fixes before production:")
        print("   1. Fix all critical test failures")
        print("   2. Review and fix configuration issues")
        print("   3. Re-run complete validation")
        print("   4. Do not use on real accounts until all tests pass")
    
    # Save report to file
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'test_results': test_results,
        'config_checks': config_checks,
        'risk_checks': risk_checks,
        'recommendation': recommendation,
        'summary': {
            'total_tests': total_tests,
            'passed_tests': len(passed_tests),
            'critical_tests': len(critical_tests),
            'critical_passed': len(critical_passed),
            'config_passed': len(config_passed),
            'risk_passed': len(risk_passed)
        }
    }
    
    report_filename = f"production_readiness_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_filename, 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\n📄 Report saved to: {report_filename}")
    
    return recommendation == "READY_FOR_PRODUCTION"

def main():
    """Main assessment function"""
    print("Starting comprehensive production readiness assessment...")
    
    # Run all validation tests
    test_results = run_all_validation_tests()
    
    # Assess system configuration
    config_checks = assess_system_configuration()
    
    # Assess risk management
    risk_checks = assess_risk_management()
    
    # Generate comprehensive report
    is_ready = generate_production_report(test_results, config_checks, risk_checks)
    
    return is_ready

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
