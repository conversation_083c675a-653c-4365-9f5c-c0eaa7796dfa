"""
Mean Reversion Trading Strategy
"""

from typing import Dict, Any, List
from strategies.base_strategy import BaseStrategy, StrategyType, MarketData, TradingSignal

class MeanReversionStrategy(BaseStrategy):
    """
    Mean Reversion Strategy
    Trades against extreme price movements expecting return to average
    """
    
    def get_strategy_type(self) -> StrategyType:
        return StrategyType.MEAN_REVERSION
    
    def get_strategy_name(self) -> str:
        return "Mean Reversion Scalping"
    
    def get_ai_prompt(
        self, 
        market_data: MarketData,
        trade_history: List[Dict[str, Any]],
        account_info: Dict[str, Any]
    ) -> str:
        """Get AI prompt for mean reversion strategy"""
        
        recent_trades = trade_history[-15:] if len(trade_history) > 15 else trade_history
        win_rate = self._calculate_win_rate(recent_trades)
        avg_profit = self._calculate_avg_profit(recent_trades)
        
        prompt = f"""
You are executing a MEAN REVERSION trading strategy for {market_data.symbol} on {market_data.timeframe} timeframe.

STRATEGY OVERVIEW:
- Strategy Type: Mean Reversion
- Magic Number: {self.magic_number}
- Focus: Trade against extreme price movements
- Timeframe: {market_data.timeframe}
- Symbol: {market_data.symbol}

MARKET DATA ANALYSIS:
- Current Price: {market_data.current_price}
- Spread: {market_data.spread} pips
- Volume: {market_data.volume}
- Volatility: {market_data.volatility:.5f}

RECENT PERFORMANCE:
- Win Rate: {win_rate:.1f}%
- Average Profit: ${avg_profit:.2f}
- Total Recent Trades: {len(recent_trades)}

MEAN REVERSION PRINCIPLES:
1. "What goes up must come down" - extreme moves tend to reverse
2. Trade against overextended price movements
3. Look for oversold/overbought conditions
4. Quick entries and exits - don't overstay
5. Works best in ranging, non-trending markets

TECHNICAL ANALYSIS FOCUS:
- Bollinger Bands for overbought/oversold levels
- RSI divergences and extreme readings (>70 or <30)
- Stochastic oscillator for momentum exhaustion
- Support and resistance levels for reversal points
- Volume analysis for confirmation

ENTRY CRITERIA:
1. Price touching or exceeding Bollinger Band extremes
2. RSI showing overbought (>70) or oversold (<30) conditions
3. Divergence between price and momentum indicators
4. Rejection at key support/resistance levels
5. Low volume on the extreme move (lack of conviction)

EXIT CRITERIA:
1. Return to moving average (mean)
2. RSI returning to neutral zone (30-70)
3. Price reaching opposite Bollinger Band
4. Quick profit taking (mean reversion moves are often short)
5. Stop loss if trend continues against position

RISK MANAGEMENT:
- Tight stop losses (price continuing in trend direction)
- Quick profit taking (mean reversion moves are brief)
- Avoid during strong trending conditions
- Maximum hold time: {self.config.get('max_hold_hours', 4)} hours

MARKET CONDITIONS TO FAVOR:
- Ranging, sideways markets
- Low volatility environments
- After major news events (overreactions)
- During consolidation phases

MARKET CONDITIONS TO AVOID:
- Strong trending markets
- Breakout scenarios
- High impact news releases
- Low liquidity periods

Based on the 200 candles of market data provided, analyze:
1. Current market regime (trending vs ranging)
2. Overbought/oversold conditions
3. Key mean reversion levels (moving averages, VWAP)
4. Momentum divergences
5. Support/resistance levels for reversal
6. Volume patterns and confirmation
7. Optimal entry and exit timing

Provide specific trading signals with:
- Action: BUY/SELL/HOLD/CLOSE with reasoning
- Entry price at extreme levels
- Tight stop loss (trend continuation protection)
- Quick take profit targets (return to mean)
- Confidence level (0-100%)
- Risk assessment (LOW/MEDIUM/HIGH)

Remember: Mean reversion requires quick reflexes. Take profits fast and cut losses quickly.
Avoid this strategy during strong trends - the market can stay irrational longer than you can stay solvent.
"""
        return prompt
    
    def validate_signal(self, signal: TradingSignal, market_data: MarketData) -> bool:
        """Validate mean reversion signal"""
        
        # Must have tight stop loss for mean reversion
        if not signal.stop_loss:
            return False
        
        # Confidence should be high for mean reversion (quick moves)
        if signal.confidence < 0.7:
            return False
        
        # Check spread conditions (important for scalping)
        if market_data.spread > self.config.get('max_spread', 1.5):
            return False
        
        # Risk-reward ratio should be reasonable but can be lower for high win rate
        if signal.entry_price and signal.stop_loss and signal.take_profit:
            risk = abs(signal.entry_price - signal.stop_loss)
            reward = abs(signal.take_profit - signal.entry_price)
            if reward / risk < 1.0:  # Minimum 1:1 RR for mean reversion
                return False
        
        return True
    
    def _calculate_win_rate(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate win rate from trade history"""
        if not trades:
            return 0.0
        
        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        return (winning_trades / len(trades)) * 100
    
    def _calculate_avg_profit(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate average profit from trade history"""
        if not trades:
            return 0.0
        
        total_profit = sum(trade.get('profit', 0) for trade in trades)
        return total_profit / len(trades)
