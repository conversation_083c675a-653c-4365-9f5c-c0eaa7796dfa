#!/usr/bin/env python3
"""
Test script to verify multiple TP risk management
"""

import sys
sys.path.append('src')

from money_management.percent_risk import PercentRiskStrategy
from money_management.base_strategy import AccountInfo

def test_multiple_tp_risk_calculation():
    """Test how multiple TP levels should calculate risk"""
    print("=" * 60)
    print("TESTING MULTIPLE TP RISK MANAGEMENT")
    print("=" * 60)
    
    # Your account setup
    balance = 75.51
    account_info = AccountInfo(
        balance=balance,
        equity=balance,
        margin=0,
        free_margin=balance/2,
        margin_level=1000,
        currency="USD",
        leverage=500
    )
    
    # Money management config
    config = {"risk_percent": 0.5}
    strategy = PercentRiskStrategy(config)
    
    # Market data
    market_data = {
        "pip_value": 10.0,
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    # Simulate the signal from the logs
    signal = {
        "entry_price": 1.15392,
        "stop_loss": 1.15542,
        "take_profit_levels": [
            {"price": 1.15242, "volume_percent": 50},
            {"price": 1.15092, "volume_percent": 30},
            {"price": 1.14942, "volume_percent": 20}
        ]
    }
    
    print(f"Account Balance: ${balance}")
    print(f"Risk Percent: {config['risk_percent']}%")
    print(f"Expected Risk: ${balance * (config['risk_percent']/100):.2f}")
    print(f"Entry: {signal['entry_price']}")
    print(f"Stop Loss: {signal['stop_loss']}")
    
    # Calculate stop loss distance
    pip_difference = abs(signal['entry_price'] - signal['stop_loss']) / market_data['pip_size']
    print(f"Stop Loss Distance: {pip_difference:.0f} pips")
    print()
    
    # Calculate base volume using money management
    trade_params = strategy.calculate_position_size(
        account_info=account_info,
        symbol="EURUSD",
        entry_price=signal['entry_price'],
        stop_loss=signal['stop_loss'],
        trade_history=[],
        market_data=market_data
    )
    
    print("MONEY MANAGEMENT CALCULATION:")
    print(f"  Base Volume: {trade_params.volume:.2f} lots")
    print(f"  Base Risk: ${trade_params.risk_amount:.2f}")
    print()
    
    # Calculate each TP level
    print("MULTIPLE TP LEVELS:")
    total_volume = 0
    total_risk = 0
    
    for i, tp_level in enumerate(signal['take_profit_levels'], 1):
        volume_percent = tp_level['volume_percent']
        tp_volume = round(trade_params.volume * (volume_percent / 100), 2)
        
        # Ensure minimum volume
        if tp_volume < market_data['min_volume']:
            tp_volume = market_data['min_volume']
        
        # Calculate risk for this TP level
        tp_risk = pip_difference * market_data['pip_value'] * tp_volume
        risk_percent_actual = (tp_risk / balance) * 100
        
        total_volume += tp_volume
        total_risk += tp_risk
        
        print(f"  TP{i} ({volume_percent}%):")
        print(f"    Price: {tp_level['price']}")
        print(f"    Volume: {tp_volume:.2f} lots")
        print(f"    Risk: ${tp_risk:.2f} ({risk_percent_actual:.2f}% of balance)")
        print()
    
    # Summary
    total_risk_percent = (total_risk / balance) * 100
    expected_risk = balance * (config['risk_percent'] / 100)
    risk_multiplier = total_risk / expected_risk
    
    print("SUMMARY:")
    print(f"  Total Volume: {total_volume:.2f} lots")
    print(f"  Total Risk: ${total_risk:.2f}")
    print(f"  Total Risk %: {total_risk_percent:.2f}%")
    print(f"  Expected Risk: ${expected_risk:.2f}")
    print(f"  Risk Multiplier: {risk_multiplier:.1f}x")
    print()
    
    if risk_multiplier > 5:
        print("⚠️  WARNING: Risk is significantly higher than intended!")
        print("   This is due to minimum volume constraints.")
    elif risk_multiplier > 2:
        print("⚠️  CAUTION: Risk is moderately higher than intended.")
    else:
        print("✅ ACCEPTABLE: Risk is within reasonable bounds.")

def test_improved_calculation():
    """Test what the improved calculation should look like"""
    print("\n" + "=" * 60)
    print("IMPROVED MULTIPLE TP CALCULATION")
    print("=" * 60)
    
    balance = 75.51
    risk_percent = 0.5
    expected_total_risk = balance * (risk_percent / 100)
    
    print(f"Target: Distribute ${expected_total_risk:.2f} risk across 3 TP levels")
    print()
    
    # Distribute risk proportionally
    tp_percentages = [50, 30, 20]
    pip_difference = 15  # From the example
    pip_value = 10.0
    min_volume = 0.01
    
    for i, percent in enumerate(tp_percentages, 1):
        tp_risk_target = expected_total_risk * (percent / 100)
        tp_volume_calculated = tp_risk_target / (pip_difference * pip_value)
        tp_volume_actual = max(tp_volume_calculated, min_volume)
        tp_risk_actual = pip_difference * pip_value * tp_volume_actual
        
        print(f"TP{i} ({percent}%):")
        print(f"  Target Risk: ${tp_risk_target:.2f}")
        print(f"  Calculated Volume: {tp_volume_calculated:.4f} lots")
        print(f"  Actual Volume: {tp_volume_actual:.2f} lots")
        print(f"  Actual Risk: ${tp_risk_actual:.2f}")
        print()

if __name__ == "__main__":
    test_multiple_tp_risk_calculation()
    test_improved_calculation()
