#!/usr/bin/env python3
"""
AI-Driven Trading System Main Entry Point
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add src to Python path
src_path = str(Path(__file__).parent / "src")
if src_path not in sys.path:
    sys.path.insert(0, src_path)

# Set PYTHONPATH environment variable
os.environ['PYTHONPATH'] = src_path

from logging_system.logger import setup_logger, get_logger

async def main():
    """Main application entry point"""
    # Load environment variables
    load_dotenv()

    # Setup logging
    logger = setup_logger()
    logger.info("Starting AI-Driven Trading System")

    try:
        # Test basic imports first
        logger.info("Testing system imports...")

        # Import components dynamically to handle import issues
        try:
            from account_management.account_manager import AccountManager
            logger.info("✓ Account manager imported")
        except ImportError as e:
            logger.error(f"Failed to import AccountManager: {e}")
            return

        try:
            from signal_generation.signal_generator import SignalGenerator
            logger.info("✓ Signal generator imported")
        except ImportError as e:
            logger.error(f"Failed to import SignalGenerator: {e}")
            return

        try:
            from trade_management.trade_manager import TradeManager
            logger.info("✓ Trade manager imported")
        except ImportError as e:
            logger.error(f"Failed to import TradeManager: {e}")
            return

        # Initialize components
        logger.info("Initializing account manager...")
        account_manager = AccountManager()

        if not account_manager.load_accounts():
            logger.error("Failed to load account configuration")
            return

        logger.info(f"Loaded {len(account_manager.accounts)} trading accounts")

        # Initialize signal generator and trade manager
        logger.info("Initializing signal generator and trade manager...")
        signal_generator = SignalGenerator(account_manager)
        trade_manager = TradeManager(account_manager)

        # Start the trading system
        logger.info("Starting signal generation and trade management...")

        # Run both components concurrently
        await asyncio.gather(
            signal_generator.start(),
            trade_manager.start()
        )
        
    except KeyboardInterrupt:
        logger.info("Shutting down trading system...")
    except Exception as e:
        logger.error(f"Fatal error in trading system: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
