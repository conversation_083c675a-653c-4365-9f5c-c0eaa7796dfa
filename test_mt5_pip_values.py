#!/usr/bin/env python3
"""
Test script to check actual MT5 pip values and account info
"""

import MetaTrader5 as mt5
import sys

def test_mt5_pip_values():
    """Test actual MT5 pip values"""
    
    # Initialize MT5
    if not mt5.initialize():
        print('MT5 initialization failed')
        sys.exit(1)

    try:
        # Test EURUSD specifically
        symbol = 'EURUSD'
        info = mt5.symbol_info(symbol)
        if info:
            print(f'{symbol} detailed info:')
            print(f'  digits: {info.digits}')
            print(f'  point: {info.point}')
            print(f'  trade_tick_value: {info.trade_tick_value}')
            print(f'  trade_tick_size: {info.trade_tick_size}')
            print(f'  volume_min: {info.volume_min}')
            print(f'  volume_step: {info.volume_step}')
            print()
            
            # Calculate pip value manually
            # For EURUSD: 1 pip = 0.0001, tick_size = 0.00001
            # So 1 pip = 10 ticks
            # If 1 tick = $1 for 1 lot, then 1 pip = $10 for 1 lot
            ticks_per_pip = 0.0001 / info.trade_tick_size
            pip_value_per_lot = info.trade_tick_value * ticks_per_pip
            print(f'Manual calculation:')
            print(f'  ticks_per_pip: {ticks_per_pip}')
            print(f'  pip_value_per_lot: ${pip_value_per_lot}')
            print()
            
        # Test with actual account currency
        account_info = mt5.account_info()
        if account_info:
            print(f'Account info:')
            print(f'  Currency: {account_info.currency}')
            print(f'  Balance: ${account_info.balance}')
            print(f'  Equity: ${account_info.equity}')
            print(f'  Leverage: {account_info.leverage}')
            print()
            
        # Test volume calculations with real scenarios
        print("=== REAL SCENARIO TESTING ===")
        balance = account_info.balance if account_info else 75
        risk_percent = 0.5
        risk_amount = balance * (risk_percent / 100)
        
        print(f"Account balance: ${balance}")
        print(f"Risk percent: {risk_percent}%")
        print(f"Risk amount: ${risk_amount}")
        print()
        
        # Test different stop loss scenarios
        scenarios = [10, 15, 20, 25, 30, 35, 40]
        pip_value = 10.0  # For EURUSD
        min_volume = 0.01
        
        for sl_pips in scenarios:
            calculated_volume = risk_amount / (sl_pips * pip_value)

            # Current logic (WRONG)
            forced_min_volume = max(min_volume, calculated_volume)  # Line 54 in percent_risk.py
            rounded_volume = round(forced_min_volume / min_volume) * min_volume  # Line 55
            actual_risk = sl_pips * pip_value * rounded_volume

            print(f"SL {sl_pips} pips: calc_vol={calculated_volume:.6f}, forced_min={forced_min_volume:.2f}, rounded_vol={rounded_volume:.2f}, risk=${actual_risk:.2f}")

        print()
        print("=== TESTING SCENARIOS THAT COULD LEAD TO 0.02, 0.03, 0.04 ===")

        # Test with higher risk percentages or different account balances
        test_cases = [
            (75, 1.0),   # 1% risk
            (75, 1.5),   # 1.5% risk
            (75, 2.0),   # 2% risk
            (100, 0.5),  # 0.5% risk on $100 account
            (100, 1.0),  # 1% risk on $100 account
            (150, 0.5),  # 0.5% risk on $150 account
        ]

        for balance, risk_pct in test_cases:
            risk_amt = balance * (risk_pct / 100)
            print(f"\nBalance: ${balance}, Risk: {risk_pct}% (${risk_amt})")

            for sl_pips in [20, 30, 40]:
                calculated_volume = risk_amt / (sl_pips * pip_value)
                forced_min_volume = max(min_volume, calculated_volume)
                rounded_volume = round(forced_min_volume / min_volume) * min_volume
                actual_risk = sl_pips * pip_value * rounded_volume

                print(f"  SL {sl_pips}: calc={calculated_volume:.4f}, final={rounded_volume:.2f}, risk=${actual_risk:.2f}")

        print()
        print("=== FINDING SCENARIOS FOR 0.02, 0.03, 0.04 VOLUMES ===")

        # To get 0.02 volume, we need calculated volume between 0.015-0.024
        # To get 0.03 volume, we need calculated volume between 0.025-0.034
        # To get 0.04 volume, we need calculated volume between 0.035-0.044

        # For 0.02 volume (need calc vol ~0.02)
        # risk_amount / (sl_pips * pip_value) = 0.02
        # risk_amount = 0.02 * sl_pips * pip_value

        target_volumes = [0.015, 0.025, 0.035]  # These should round to 0.02, 0.03, 0.04

        for target_vol in target_volumes:
            print(f"\nTarget calculated volume: {target_vol}")
            for sl_pips in [20, 30, 40]:
                required_risk = target_vol * sl_pips * pip_value
                required_balance_05pct = required_risk / 0.005  # For 0.5% risk
                required_balance_1pct = required_risk / 0.01    # For 1% risk

                # Test the calculation
                calc_vol = required_risk / (sl_pips * pip_value)
                forced_vol = max(min_volume, calc_vol)
                rounded_vol = round(forced_vol / min_volume) * min_volume

                print(f"  SL {sl_pips}: need_risk=${required_risk:.2f}, balance_0.5%=${required_balance_05pct:.0f}, balance_1%=${required_balance_1pct:.0f}, final_vol={rounded_vol:.2f}")
            
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    test_mt5_pip_values()
