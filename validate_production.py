#!/usr/bin/env python3
"""
Production Validation Script for AI-Driven Trading System
Tests the system with real MT5 connections and API calls
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv
import json

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from account_management.account_manager import AccountManager
from mt5_integration.mt5_client import MT5Client
from ai_integration.qwen_client import QwenClient
from logging_system.logger import setup_logger, get_logger

# Setup logging
setup_logger()
logger = get_logger(__name__)

class ProductionValidator:
    """Validates the trading system in production environment"""
    
    def __init__(self):
        self.account_manager = None
        self.mt5_client = None
        self.qwen_client = None
        self.validation_results = {}
    
    def validate_environment(self) -> bool:
        """Validate environment configuration"""
        logger.info("Validating environment configuration...")
        
        # Load environment variables
        load_dotenv()
        
        required_vars = [
            'QWEN_API_KEY',
            'MT5_PATH'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"Missing environment variables: {missing_vars}")
            self.validation_results['environment'] = False
            return False
        
        logger.info("✓ Environment configuration valid")
        self.validation_results['environment'] = True
        return True
    
    def validate_configuration(self) -> bool:
        """Validate account configuration"""
        logger.info("Validating account configuration...")
        
        try:
            self.account_manager = AccountManager()
            if not self.account_manager.load_accounts():
                logger.error("Failed to load account configuration")
                self.validation_results['configuration'] = False
                return False
            
            if len(self.account_manager.accounts) == 0:
                logger.error("No accounts configured")
                self.validation_results['configuration'] = False
                return False
            
            logger.info(f"✓ Configuration valid - {len(self.account_manager.accounts)} accounts loaded")
            self.validation_results['configuration'] = True
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            self.validation_results['configuration'] = False
            return False
    
    def validate_mt5_connection(self) -> bool:
        """Validate MT5 connection"""
        logger.info("Validating MT5 connection...")
        
        try:
            self.mt5_client = MT5Client()
            
            # Test with first account
            first_account = next(iter(self.account_manager.accounts.values()))
            
            if not self.mt5_client.login(first_account):
                logger.error("Failed to connect to MT5")
                self.validation_results['mt5_connection'] = False
                return False
            
            # Test basic operations
            account_info = self.mt5_client.get_account_info()
            if not account_info:
                logger.error("Failed to get account info from MT5")
                self.validation_results['mt5_connection'] = False
                return False
            
            logger.info(f"✓ MT5 connection valid - Account: {account_info.login}, Balance: ${account_info.balance:.2f}")
            self.validation_results['mt5_connection'] = True
            return True
            
        except Exception as e:
            logger.error(f"MT5 connection validation failed: {e}")
            self.validation_results['mt5_connection'] = False
            return False
    
    def validate_market_data(self) -> bool:
        """Validate market data retrieval"""
        logger.info("Validating market data retrieval...")
        
        try:
            # Test with first account's first symbol
            first_account = next(iter(self.account_manager.accounts.values()))
            first_symbol = first_account.symbols[0]['symbol']
            first_timeframe = first_account.symbols[0]['timeframe']
            
            market_data = self.mt5_client.get_market_data(first_symbol, first_timeframe, 200)
            if not market_data or len(market_data.candles) == 0:
                logger.error(f"Failed to get market data for {first_symbol}")
                self.validation_results['market_data'] = False
                return False
            
            logger.info(f"✓ Market data valid - {first_symbol}: {len(market_data.candles)} candles retrieved")
            self.validation_results['market_data'] = True
            return True
            
        except Exception as e:
            logger.error(f"Market data validation failed: {e}")
            self.validation_results['market_data'] = False
            return False
    
    async def validate_ai_connection(self) -> bool:
        """Validate AI API connection"""
        logger.info("Validating AI API connection...")
        
        try:
            self.qwen_client = QwenClient()
            
            # Test with simple prompt
            test_prompt = """
            This is a test prompt for the AI trading system.
            Please respond with a simple JSON object containing:
            {"status": "ok", "message": "AI connection successful"}
            """
            
            response = await self.qwen_client.get_trading_decision(test_prompt)
            if not response:
                logger.error("Failed to get response from AI API")
                self.validation_results['ai_connection'] = False
                return False
            
            logger.info("✓ AI API connection valid")
            self.validation_results['ai_connection'] = True
            return True
            
        except Exception as e:
            logger.error(f"AI API validation failed: {e}")
            self.validation_results['ai_connection'] = False
            return False
    
    def validate_strategies_and_money_management(self) -> bool:
        """Validate strategy and money management factories"""
        logger.info("Validating strategies and money management...")
        
        try:
            # Test all configured strategies
            strategies_tested = set()
            mm_strategies_tested = set()
            
            for account in self.account_manager.accounts.values():
                # Test strategy
                strategy = self.account_manager.strategy_factory.create_strategy(account.strategy)
                if not strategy:
                    logger.error(f"Failed to create strategy: {account.strategy}")
                    self.validation_results['strategies'] = False
                    return False
                strategies_tested.add(account.strategy)
                
                # Test money management
                mm_strategy = self.account_manager.money_management_factory.create_strategy(account.money_management)
                if not mm_strategy:
                    logger.error(f"Failed to create money management: {account.money_management}")
                    self.validation_results['strategies'] = False
                    return False
                mm_strategies_tested.add(account.money_management)
            
            logger.info(f"✓ Strategies valid - Tested: {strategies_tested}")
            logger.info(f"✓ Money management valid - Tested: {mm_strategies_tested}")
            self.validation_results['strategies'] = True
            return True
            
        except Exception as e:
            logger.error(f"Strategy validation failed: {e}")
            self.validation_results['strategies'] = False
            return False
    
    async def run_full_validation(self) -> bool:
        """Run complete validation suite"""
        logger.info("Starting production validation...")
        logger.info("=" * 60)
        
        validations = [
            ("Environment", self.validate_environment),
            ("Configuration", self.validate_configuration),
            ("MT5 Connection", self.validate_mt5_connection),
            ("Market Data", self.validate_market_data),
            ("AI Connection", self.validate_ai_connection),
            ("Strategies", self.validate_strategies_and_money_management)
        ]
        
        passed = 0
        total = len(validations)
        
        for name, validation_func in validations:
            logger.info(f"\nValidating {name}...")
            try:
                if asyncio.iscoroutinefunction(validation_func):
                    result = await validation_func()
                else:
                    result = validation_func()
                
                if result:
                    passed += 1
                    logger.info(f"✓ {name} validation passed")
                else:
                    logger.error(f"✗ {name} validation failed")
            except Exception as e:
                logger.error(f"✗ {name} validation error: {e}")
        
        logger.info("\n" + "=" * 60)
        logger.info(f"Validation Results: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All validations passed! System is ready for production.")
            logger.info("\nYou can now run: python main.py")
        else:
            logger.error("❌ Some validations failed. Please fix the issues before running the system.")
            logger.info("\nFailed validations:")
            for key, value in self.validation_results.items():
                if not value:
                    logger.error(f"  - {key}")
        
        return passed == total

async def main():
    """Main validation function"""
    validator = ProductionValidator()
    success = await validator.run_full_validation()
    return 0 if success else 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
