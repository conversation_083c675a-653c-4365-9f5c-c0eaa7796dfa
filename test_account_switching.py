#!/usr/bin/env python3
"""
Account Switching Diagnostic Test
Tests account switching functionality with comprehensive diagnostics
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

from dotenv import load_dotenv
from logging_system.logger import setup_logger, get_logger
from account_management.account_manager import AccountManager
from mt5_integration.session_manager import session_manager
from diagnostics.account_switch_diagnostics import account_switch_diagnostics

# Load environment variables
load_dotenv()

logger = setup_logger()


async def test_account_switching():
    """Test account switching with diagnostics"""
    logger.info("🧪 Starting Account Switching Diagnostic Test")
    logger.info("=" * 60)
    
    try:
        # Load accounts
        account_manager = AccountManager()
        if not account_manager.load_accounts():
            logger.error("❌ Failed to load accounts")
            return False
        
        accounts = account_manager.get_all_accounts()
        if len(accounts) < 2:
            logger.warning("⚠️ Need at least 2 accounts to test switching")
            if len(accounts) == 1:
                logger.info("Testing single account operations...")
                await test_single_account(accounts[0])
            return True
        
        logger.info(f"📋 Found {len(accounts)} accounts for testing")
        
        # Test switching between accounts
        for i in range(len(accounts)):
            current_account = accounts[i]
            next_account = accounts[(i + 1) % len(accounts)]
            
            logger.info(f"\n🔄 Test {i + 1}: Switching from {current_account.account_id} to {next_account.account_id}")
            logger.info("-" * 50)
            
            # Test the switch
            success = await session_manager.ensure_account_session(next_account, None)
            
            if success:
                logger.info(f"✅ Switch successful: {current_account.account_id} -> {next_account.account_id}")
            else:
                logger.error(f"❌ Switch failed: {current_account.account_id} -> {next_account.account_id}")
            
            # Wait between tests
            await asyncio.sleep(2)
        
        # Print diagnostic summary
        await print_diagnostic_summary()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test error: {e}")
        return False


async def test_single_account(account):
    """Test single account operations"""
    logger.info(f"🔍 Testing single account: {account.account_id}")
    
    # Test multiple connections to same account
    for i in range(3):
        logger.info(f"🔄 Connection test {i + 1}/3")
        success = await session_manager.ensure_account_session(account, None)
        
        if success:
            logger.info(f"✅ Connection {i + 1} successful")
        else:
            logger.error(f"❌ Connection {i + 1} failed")
        
        await asyncio.sleep(1)


async def print_diagnostic_summary():
    """Print comprehensive diagnostic summary"""
    logger.info("\n" + "=" * 60)
    logger.info("📊 DIAGNOSTIC SUMMARY")
    logger.info("=" * 60)
    
    summary = account_switch_diagnostics.get_diagnostic_summary()
    
    logger.info(f"Total switch attempts: {summary['total_attempts']}")
    logger.info(f"Successful switches: {summary['successful_switches']}")
    logger.info(f"Success rate: {summary['success_rate']:.1f}%")
    
    if summary['last_successful_account']:
        logger.info(f"Last successful account: {summary['last_successful_account']}")
    
    if summary['recent_issues']:
        logger.info("\n🚨 Recent Issues:")
        for i, issues in enumerate(summary['recent_issues'][-3:], 1):
            logger.info(f"  {i}. {issues}")
    
    if summary['common_recommendations']:
        logger.info("\n💡 Common Recommendations:")
        for i, rec in enumerate(summary['common_recommendations'], 1):
            logger.info(f"  {i}. {rec}")
    
    # Print detailed history
    logger.info("\n📋 Detailed Diagnostic History:")
    for i, diagnosis in enumerate(account_switch_diagnostics.diagnostic_history[-5:], 1):
        logger.info(f"\n  Diagnosis {i}:")
        logger.info(f"    Timestamp: {diagnosis['timestamp']}")
        logger.info(f"    From: {diagnosis['from_account']} -> To: {diagnosis['to_account']}")
        logger.info(f"    Success: {diagnosis['success']}")
        
        if diagnosis['issues_found']:
            logger.info(f"    Issues: {diagnosis['issues_found']}")
        
        if diagnosis['recommendations']:
            logger.info(f"    Recommendations: {diagnosis['recommendations']}")
        
        # Print key metrics
        switch_process = diagnosis.get('switch_process', {})
        if switch_process.get('login_duration'):
            logger.info(f"    Login duration: {switch_process['login_duration']:.2f}s")
        if switch_process.get('retry_attempts'):
            logger.info(f"    Retry attempts: {switch_process['retry_attempts']}")


async def test_mt5_connection_stability():
    """Test MT5 connection stability"""
    logger.info("\n🔍 Testing MT5 Connection Stability")
    logger.info("-" * 40)
    
    import MetaTrader5 as mt5
    
    # Test multiple initializations
    for i in range(5):
        logger.info(f"🔄 Initialization test {i + 1}/5")
        
        # Initialize
        if mt5.initialize():
            logger.info("✅ MT5 initialized")
            
            # Get terminal info
            terminal_info = mt5.terminal_info()
            if terminal_info:
                logger.info(f"  Terminal connected: {terminal_info.connected}")
                logger.info(f"  Terminal build: {terminal_info.build}")
            
            # Test shutdown and reinitialize
            mt5.shutdown()
            logger.info("🔄 MT5 shutdown")
            
        else:
            logger.error("❌ MT5 initialization failed")
        
        await asyncio.sleep(1)


async def main():
    """Main test function"""
    logger.info("🤖 Account Switching Diagnostic Test Suite")
    logger.info("=" * 60)
    
    try:
        # Test MT5 connection stability first
        await test_mt5_connection_stability()
        
        # Test account switching
        await test_account_switching()
        
        logger.info("\n✅ All tests completed")
        
    except KeyboardInterrupt:
        logger.info("\n🛑 Test interrupted by user")
    except Exception as e:
        logger.error(f"❌ Test suite error: {e}")
        return False
    
    return True


if __name__ == "__main__":
    asyncio.run(main())
