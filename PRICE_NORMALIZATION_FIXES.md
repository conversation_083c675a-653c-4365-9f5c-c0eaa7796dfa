# Price Normalization Fixes for MT5 Integration

## Problem Description

The trading system was experiencing "Invalid price" errors (MT5 error code 10015) when modifying orders and positions. This error occurs when prices are not properly formatted according to the symbol's decimal precision requirements.

**Error Example:**
```
2025-08-05 03:39:04 | ERROR | mt5_integration.mt5_client:modify_order:1094 - Order modification failed: 10015 - Invalid price
```

## Root Cause

MT5 is very strict about price precision. Each symbol has a specific number of decimal places (digits) that must be respected:
- EURUSD: 5 digits (1.12345)
- USDJPY: 3 digits (150.123)
- XAUUSD: 2 digits (2500.12)

When prices are passed with incorrect precision (e.g., 1.123456789 instead of 1.12346), MT5 rejects them with error 10015.

## Solution Implemented

### 1. Added Price Normalization Methods to MT5Client

#### `_normalize_price(symbol, price)`
- Retrieves symbol information to get the correct number of digits
- Rounds the price to the appropriate decimal places
- Provides fallback to 5 digits for forex pairs

#### `_validate_price_for_symbol(symbol, price, order_type)`
- Normalizes price to correct decimal places
- Validates price is within reasonable range relative to current market
- Adjusts prices that are too far from current market prices

### 2. Updated Order and Position Modification Functions

#### `modify_order()` Enhancements:
- Added comprehensive price normalization for entry price, stop loss, and take profit
- Enhanced error handling specifically for error code 10015
- Added retry logic with market-based prices when normalization fails
- Improved debugging output

#### `modify_position()` Enhancements:
- Added price normalization for stop loss and take profit modifications
- Enhanced error handling for invalid price errors
- Added detailed debugging information

#### `place_order()` Enhancements:
- Added price normalization for all price parameters before order placement
- Ensures all prices are properly formatted before sending to MT5

### 3. Enhanced Trade Validator

#### Added `_normalize_price()` method to TradeValidator:
- Consistent price normalization across validation layer
- Automatic price correction during validation

#### Updated validation methods:
- `validate_signal_execution()` now normalizes prices before validation
- `validate_modification()` normalizes prices for position/order modifications
- Automatic correction of price parameters with proper logging

### 4. Enhanced Error Handling

#### Specific handling for MT5 error code 10015:
- Detailed logging of current market conditions
- Retry logic with market-based prices
- Fallback mechanisms for price adjustment

## Code Changes Summary

### Files Modified:

1. **`src/mt5_integration/mt5_client.py`**
   - Added `_normalize_price()` method
   - Added `_validate_price_for_symbol()` method
   - Updated `modify_order()` with price normalization
   - Updated `modify_position()` with price normalization
   - Updated `place_order()` with price normalization
   - Enhanced error handling for error 10015

2. **`src/validation/trade_validator.py`**
   - Added `_normalize_price()` method
   - Updated `validate_signal_execution()` to normalize prices
   - Updated `validate_modification()` to normalize prices
   - Added automatic price correction with logging

### Key Methods Added:

```python
def _normalize_price(self, symbol: str, price: float) -> float:
    """Normalize price to the correct number of decimal places for the symbol"""
    
def _validate_price_for_symbol(self, symbol: str, price: float, order_type: str = "MARKET") -> float:
    """Validate and adjust price according to symbol specifications"""
```

## Testing

Created `test_price_normalization_fix.py` to verify:
- Price normalization functionality
- Symbol digits retrieval
- Order modification scenarios
- Trade validator integration

## Expected Results

After implementing these fixes:

1. **Eliminated "Invalid price" errors (10015)** - All prices are now properly normalized
2. **Improved order modification reliability** - Consistent price formatting
3. **Enhanced error recovery** - Automatic retry with corrected prices
4. **Better debugging** - Detailed logging of price normalization steps
5. **Consistent validation** - Price normalization across all components

## Usage Examples

### Before Fix:
```python
# This could fail with error 10015
client.modify_order(ticket=12345, price=1.123456789)
```

### After Fix:
```python
# This will automatically normalize to correct precision
client.modify_order(ticket=12345, price=1.123456789)  # Becomes 1.12346
```

## Monitoring

To monitor the effectiveness of these fixes:

1. Check logs for price normalization messages:
   ```
   🔍 MODIFY_ORDER_DEBUG: Price normalized from 1.123456789 to 1.12346
   ```

2. Monitor for reduction in error 10015 occurrences

3. Verify successful order/position modifications

## Future Enhancements

1. **Symbol-specific validation rules** - Different validation for different asset classes
2. **Price step validation** - Ensure prices align with minimum price steps
3. **Market hours validation** - Additional checks for market open/close times
4. **Performance optimization** - Cache symbol information to reduce API calls

## Conclusion

These comprehensive price normalization fixes address the root cause of MT5 "Invalid price" errors by ensuring all prices are properly formatted according to each symbol's specifications. The implementation includes robust error handling, automatic correction, and detailed logging for monitoring and debugging.
