#!/usr/bin/env python3
"""
AI-Driven Trading System - Main Application
Fixed version that resolves import issues
"""

import asyncio
import sys
import os
import signal
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

# Now import with absolute paths
from logging_system.logger import setup_logger, get_logger
from account_management.account_manager import AccountManager
from signal_generation.signal_generator import SignalGenerator
from trade_management.trade_manager import TradeManager
from scheduling.scheduler_coordinator import scheduler_coordinator

class TradingSystem:
    """Main trading system orchestrator"""
    
    def __init__(self):
        self.logger = None
        self.account_manager = None
        self.signal_generator = None
        self.trade_manager = None
        self.running = False
    
    async def initialize(self):
        """Initialize all system components"""
        try:
            # Setup logging
            self.logger = setup_logger()
            self.logger.info("🚀 AI-Driven Trading System Initializing...")
            
            # Load environment variables
            load_dotenv()
            
            # Initialize account manager
            self.logger.info("📊 Initializing Account Manager...")
            self.account_manager = AccountManager()
            
            if not self.account_manager.load_accounts():
                self.logger.error("❌ Failed to load accounts")
                return False
            
            self.logger.info(f"✅ Loaded {len(self.account_manager.accounts)} trading accounts")
            
            # Log account details
            for account_id, account in self.account_manager.accounts.items():
                self.logger.info(f"   Account: {account.account_id} | Strategy: {account.strategy_type} | MM: {account.money_management_type}")
            
            # Initialize signal generator
            self.logger.info("🎯 Initializing Signal Generator...")
            self.signal_generator = SignalGenerator(self.account_manager)
            
            # Initialize trade manager
            self.logger.info("📈 Initializing Trade Manager...")
            self.trade_manager = TradeManager(self.account_manager)
            
            self.logger.info("✅ All components initialized successfully")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"❌ Initialization failed: {e}")
            else:
                print(f"❌ Initialization failed: {e}")
            return False
    
    async def start(self):
        """Start the trading system"""
        if not await self.initialize():
            return False
        
        self.running = True
        self.logger.info("🎯 Starting Trading Operations...")
        self.logger.info("=" * 60)
        
        try:
            # Start both components concurrently
            await asyncio.gather(
                self.run_signal_generation(),
                self.run_trade_management(),
                return_exceptions=True
            )
        except Exception as e:
            self.logger.error(f"❌ System error: {e}")
            return False
        finally:
            self.running = False
            self.logger.info("🛑 Trading system stopped")
    
    async def run_signal_generation(self):
        """Run signal generation loop with scheduling coordination"""
        self.logger.info("🔄 Signal generation started")

        try:
            while self.running:
                # Check if signal generation can run
                if await scheduler_coordinator.can_run_signal_generation():
                    # Mark as starting
                    if await scheduler_coordinator.start_signal_generation():
                        try:
                            self.logger.info("📡 Generating trading signals...")

                            # Generate signals for all accounts
                            await self.signal_generator.generate_signals()

                            self.logger.info("✅ Signal generation completed")
                        finally:
                            # Mark as finished
                            await scheduler_coordinator.finish_signal_generation()
                    else:
                        self.logger.debug("⏸️ Signal generation blocked by scheduler")
                else:
                    # Get next scheduled time
                    next_time = scheduler_coordinator.get_next_signal_generation_time()
                    if next_time:
                        time_to_next = (next_time - datetime.now()).total_seconds()
                        if time_to_next > 0:
                            self.logger.debug(f"⏰ Next signal generation in {time_to_next:.0f} seconds")

                # Wait before checking again
                await asyncio.sleep(60)  # Check every minute

        except Exception as e:
            self.logger.error(f"❌ Signal generation error: {e}")
            await scheduler_coordinator.finish_signal_generation()  # Ensure state is reset
            raise
    
    async def run_trade_management(self):
        """Run trade management loop with scheduling coordination"""
        self.logger.info("🔄 Trade management started")

        try:
            while self.running:
                # Check if trade management can run
                if await scheduler_coordinator.can_run_trade_management():
                    # Mark as starting
                    if await scheduler_coordinator.start_trade_management():
                        try:
                            self.logger.info("📊 Managing existing trades...")

                            # Manage existing trades
                            await self.trade_manager.manage_trades()

                            self.logger.info("✅ Trade management completed")
                        finally:
                            # Mark as finished
                            await scheduler_coordinator.finish_trade_management()
                    else:
                        self.logger.debug("⏸️ Trade management blocked by scheduler")
                else:
                    # Get next scheduled time
                    next_time = scheduler_coordinator.get_next_trade_management_time()
                    if next_time:
                        time_to_next = (next_time - datetime.now()).total_seconds()
                        if time_to_next > 0:
                            self.logger.debug(f"⏰ Next trade management in {time_to_next:.0f} seconds")

                # Wait before checking again
                await asyncio.sleep(60)  # Check every minute

        except Exception as e:
            self.logger.error(f"❌ Trade management error: {e}")
            await scheduler_coordinator.finish_trade_management()  # Ensure state is reset
            raise
    
    def stop(self):
        """Stop the trading system"""
        self.running = False
        if self.logger:
            self.logger.info("🛑 Shutdown signal received")

async def main():
    """Main application entry point"""
    print("🤖 AI-Driven Trading System")
    print("=" * 50)
    
    # Create trading system
    trading_system = TradingSystem()
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        print("\n🛑 Shutdown signal received...")
        trading_system.stop()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start the trading system
        await trading_system.start()
    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt received")
        trading_system.stop()
    except Exception as e:
        print(f"❌ System error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting AI-Driven Trading System...")
    print("Press Ctrl+C to stop the system")
    print()
    
    success = asyncio.run(main())
    
    if success:
        print("✅ System shutdown complete")
    else:
        print("❌ System encountered errors")
        sys.exit(1)
