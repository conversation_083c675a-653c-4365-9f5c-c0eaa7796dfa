#!/usr/bin/env python3
"""
Final Production Validation Test
Corrected validation for Qwen AI system without Unicode issues
"""

import sys
import os
import json
from datetime import datetime
from dotenv import load_dotenv

def test_environment_setup():
    """Test environment setup for Qwen AI system"""
    print("ENVIRONMENT SETUP VALIDATION")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check required environment variables for Qwen
    required_env_vars = {
        'QWEN_API_KEY': 'Qwen AI API key',
        'QWEN_API_URL': 'Qwen API URL',
        'AI_MODEL': 'AI model name',
        'MT5_PATH': 'MetaTrader 5 path'
    }
    
    missing_vars = []
    
    for var, description in required_env_vars.items():
        value = os.getenv(var)
        if value:
            print(f"  {var}: CONFIGURED")
        else:
            print(f"  {var}: MISSING")
            missing_vars.append(var)
    
    if not missing_vars:
        print("RESULT: ALL ENVIRONMENT VARIABLES CONFIGURED")
        return True
    else:
        print(f"RESULT: MISSING VARIABLES: {missing_vars}")
        return False

def test_ai_stop_loss_logic():
    """Test AI stop loss logic without full system"""
    print("\nAI STOP LOSS VALIDATION")
    print("=" * 50)
    
    # Check prompt builder for hardcoded values
    prompt_file = os.path.join('src', 'ai_integration', 'prompt_builder.py')
    
    if not os.path.exists(prompt_file):
        print("PROMPT BUILDER FILE NOT FOUND")
        return False
    
    with open(prompt_file, 'r') as f:
        content = f.read()
    
    # Check for hardcoded pip values
    hardcoded_issues = []
    
    if '10-50 pips' in content:
        hardcoded_issues.append('10-50 pips range found')
    
    if 'pip = ' in content.lower():
        hardcoded_issues.append('Hardcoded pip assignment found')
    
    # Check for dynamic guidance
    dynamic_concepts = [
        'technical levels',
        'volatility',
        'market condition',
        'technical analysis'
    ]
    
    concepts_found = []
    for concept in dynamic_concepts:
        if concept.lower() in content.lower():
            concepts_found.append(concept)
    
    print(f"Hardcoded issues found: {len(hardcoded_issues)}")
    print(f"Dynamic concepts found: {len(concepts_found)}/{len(dynamic_concepts)}")
    
    if hardcoded_issues:
        for issue in hardcoded_issues:
            print(f"  ISSUE: {issue}")
    
    for concept in concepts_found:
        print(f"  DYNAMIC: {concept}")
    
    success = len(hardcoded_issues) == 0 and len(concepts_found) >= 3
    print(f"RESULT: {'PASS' if success else 'FAIL'}")
    return success

def test_risk_management_settings():
    """Test risk management settings"""
    print("\nRISK MANAGEMENT VALIDATION")
    print("=" * 50)
    
    # Load account configuration
    accounts_file = 'config/accounts.json'
    
    if not os.path.exists(accounts_file):
        print("ACCOUNTS CONFIGURATION NOT FOUND")
        return False
    
    try:
        with open(accounts_file, 'r') as f:
            accounts_data = json.load(f)
        
        account = accounts_data['accounts'][0]
        mm_settings = account['money_management_settings']
        
        # Check critical settings
        risk_percent = mm_settings.get('risk_percent', 0)
        max_daily_loss = mm_settings.get('max_daily_loss', 0)
        max_daily_trades = mm_settings.get('max_daily_trades', 0)
        max_open_positions = mm_settings.get('max_open_positions', 0)
        
        print(f"Risk Percent: {risk_percent}%")
        print(f"Max Daily Loss: ${max_daily_loss}")
        print(f"Max Daily Trades: {max_daily_trades}")
        print(f"Max Open Positions: {max_open_positions}")
        
        # Validate settings are reasonable
        checks = []
        
        # Risk percent should be 0.5% to 5%
        if 0.5 <= risk_percent <= 5.0:
            checks.append(('Risk Percent', True, f'{risk_percent}% is reasonable'))
        else:
            checks.append(('Risk Percent', False, f'{risk_percent}% may be too high/low'))
        
        # Daily loss should be reasonable for account size
        balance = 74.40  # Current demo balance
        daily_loss_percent = (max_daily_loss / balance) * 100
        if daily_loss_percent <= 10:
            checks.append(('Daily Loss Limit', True, f'{daily_loss_percent:.1f}% of balance'))
        else:
            checks.append(('Daily Loss Limit', False, f'{daily_loss_percent:.1f}% too high'))
        
        # Position limits should be conservative
        if 1 <= max_open_positions <= 5:
            checks.append(('Position Limit', True, f'{max_open_positions} positions reasonable'))
        else:
            checks.append(('Position Limit', False, f'{max_open_positions} positions may be too many'))
        
        # Trade limits should prevent overtrading
        if 1 <= max_daily_trades <= 10:
            checks.append(('Trade Limit', True, f'{max_daily_trades} trades reasonable'))
        else:
            checks.append(('Trade Limit', False, f'{max_daily_trades} trades may be too many'))
        
        # Display results
        passed_checks = 0
        for check_name, passed, details in checks:
            status = 'PASS' if passed else 'FAIL'
            print(f"  {check_name}: {status} - {details}")
            if passed:
                passed_checks += 1
        
        success = passed_checks == len(checks)
        print(f"RESULT: {passed_checks}/{len(checks)} checks passed")
        return success
        
    except Exception as e:
        print(f"ERROR READING CONFIGURATION: {e}")
        return False

def test_money_management_calculations():
    """Test money management calculations"""
    print("\nMONEY MANAGEMENT VALIDATION")
    print("=" * 50)
    
    # Test percent risk calculation
    balance = 74.40
    risk_percent = 2.0
    expected_risk = balance * (risk_percent / 100)
    
    print(f"Account Balance: ${balance}")
    print(f"Risk Percent: {risk_percent}%")
    print(f"Expected Risk Amount: ${expected_risk:.2f}")
    
    # Test position sizing scenarios
    scenarios = [
        {
            'symbol': 'EURUSD',
            'entry': 1.0850,
            'stop_loss': 1.0820,
            'pip_size': 0.0001,
            'pip_value': 10.0,
            'description': 'EURUSD 30 pip stop'
        },
        {
            'symbol': 'USDJPY',
            'entry': 149.85,
            'stop_loss': 149.65,
            'pip_size': 0.01,
            'pip_value': 6.67,
            'description': 'USDJPY 20 pip stop'
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"\n  {scenario['description']}:")
        
        # Calculate pip distance
        pip_distance = abs(scenario['entry'] - scenario['stop_loss']) / scenario['pip_size']
        
        # Calculate required volume for target risk
        required_volume = expected_risk / (pip_distance * scenario['pip_value'])
        
        # Apply minimum volume constraint
        min_volume = 0.01
        actual_volume = max(required_volume, min_volume)
        
        # Calculate actual risk
        actual_risk = pip_distance * scenario['pip_value'] * actual_volume
        
        print(f"    Pip Distance: {pip_distance:.1f}")
        print(f"    Required Volume: {required_volume:.4f}")
        print(f"    Actual Volume: {actual_volume:.2f}")
        print(f"    Actual Risk: ${actual_risk:.2f}")
        
        # Validate
        volume_ok = actual_volume >= min_volume
        risk_reasonable = actual_risk <= expected_risk * 10  # Allow up to 10x for small accounts
        
        scenario_passed = volume_ok and risk_reasonable
        print(f"    Result: {'PASS' if scenario_passed else 'FAIL'}")
        
        if not scenario_passed:
            all_passed = False
    
    print(f"RESULT: {'PASS' if all_passed else 'FAIL'}")
    return all_passed

def test_system_files():
    """Test system files and structure"""
    print("\nSYSTEM FILES VALIDATION")
    print("=" * 50)
    
    required_files = [
        'config/accounts.json',
        'src/ai_integration/qwen_client.py',
        'src/money_management/percent_risk.py',
        'src/signal_generation/signal_generator.py',
        'src/trade_management/trade_manager.py'
    ]
    
    required_dirs = [
        'src',
        'config',
        'logs'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  FILE: {file_path} - EXISTS")
        else:
            print(f"  FILE: {file_path} - MISSING")
            missing_files.append(file_path)
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"  DIR: {dir_path} - EXISTS")
        else:
            print(f"  DIR: {dir_path} - MISSING")
            missing_dirs.append(dir_path)
    
    success = len(missing_files) == 0 and len(missing_dirs) == 0
    print(f"RESULT: {'PASS' if success else 'FAIL'}")
    
    if missing_files:
        print(f"  Missing files: {missing_files}")
    if missing_dirs:
        print(f"  Missing directories: {missing_dirs}")
    
    return success

def main():
    """Main validation function"""
    print("FINAL PRODUCTION VALIDATION")
    print("=" * 60)
    print(f"Validation Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ('Environment Setup', test_environment_setup),
        ('AI Stop Loss Logic', test_ai_stop_loss_logic),
        ('Risk Management Settings', test_risk_management_settings),
        ('Money Management Calculations', test_money_management_calculations),
        ('System Files', test_system_files)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"ERROR IN {test_name}: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\nFINAL SUMMARY")
    print("=" * 60)
    
    passed_tests = [r for r in results if r[1]]
    total_tests = len(results)
    
    for test_name, passed in results:
        status = "PASS" if passed else "FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {len(passed_tests)}/{total_tests} tests passed")
    
    if len(passed_tests) == total_tests:
        print("\nSYSTEM STATUS: READY FOR PRODUCTION")
        print("RECOMMENDATION: System is ready for real account trading")
        print("NEXT STEPS:")
        print("  1. Start with minimum position sizes")
        print("  2. Monitor first few trades closely")
        print("  3. Verify AI decisions are reasonable")
        return True
    else:
        print("\nSYSTEM STATUS: NEEDS ATTENTION")
        print("RECOMMENDATION: Fix failing tests before production")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
