#!/usr/bin/env python3
"""
Comprehensive Code Logic Review and Validation
Reviews all critical components for logical consistency, error handling, and edge cases
"""

import sys
import os
import inspect
from pathlib import Path
from typing import List, Dict, Any

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class CodeLogicReviewer:
    """Comprehensive code logic reviewer"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.recommendations = []
    
    def add_issue(self, severity: str, component: str, description: str, recommendation: str = None):
        """Add a code issue"""
        issue = {
            'severity': severity,
            'component': component,
            'description': description,
            'recommendation': recommendation
        }
        
        if severity == 'CRITICAL':
            self.issues.append(issue)
        elif severity == 'WARNING':
            self.warnings.append(issue)
        else:
            self.recommendations.append(issue)
    
    def review_account_management(self):
        """Review account management logic"""
        print("🔍 Reviewing Account Management...")
        
        try:
            from account_management.account_manager import AccountManager
            from account_management.models import TradingAccount, AccountGroup
            
            # Check AccountManager logic
            print("  ✓ Account Manager imports successfully")
            
            # Review critical methods
            manager_methods = inspect.getmembers(AccountManager, predicate=inspect.isfunction)
            critical_methods = ['load_accounts', 'get_account', 'get_accounts_by_group']
            
            for method_name, method in manager_methods:
                if method_name in critical_methods:
                    source = inspect.getsource(method)
                    
                    # Check for error handling
                    if 'try:' not in source or 'except' not in source:
                        self.add_issue('WARNING', 'AccountManager', 
                                     f"Method {method_name} may lack proper error handling")
                    
                    # Check for validation
                    if method_name == 'load_accounts' and 'validate' not in source.lower():
                        self.add_issue('INFO', 'AccountManager',
                                     f"Consider adding validation in {method_name}")
            
            print("  ✓ Account Manager logic reviewed")
            
        except Exception as e:
            self.add_issue('CRITICAL', 'AccountManager', f"Failed to import or review: {e}")
    
    def review_money_management(self):
        """Review money management strategies"""
        print("🔍 Reviewing Money Management Strategies...")
        
        try:
            from money_management.percent_risk import PercentRiskStrategy
            from money_management.fixed_volume import FixedVolumeStrategy
            from money_management.martingale import MartingaleStrategy
            from money_management.anti_martingale import AntiMartingaleStrategy
            
            strategies = [
                ('PercentRiskStrategy', PercentRiskStrategy),
                ('FixedVolumeStrategy', FixedVolumeStrategy),
                ('MartingaleStrategy', MartingaleStrategy),
                ('AntiMartingaleStrategy', AntiMartingaleStrategy)
            ]
            
            for name, strategy_class in strategies:
                print(f"  📊 Reviewing {name}...")
                
                # Check for required methods
                required_methods = ['calculate_position_size', 'get_ai_prompt']
                for method_name in required_methods:
                    if not hasattr(strategy_class, method_name):
                        self.add_issue('CRITICAL', name, f"Missing required method: {method_name}")
                
                # Check position size calculation logic
                if hasattr(strategy_class, 'calculate_position_size'):
                    source = inspect.getsource(strategy_class.calculate_position_size)
                    
                    # Check for volume validation
                    if 'min_volume' not in source or 'max_volume' not in source:
                        self.add_issue('WARNING', name, 
                                     "Position size calculation should validate min/max volume")
                    
                    # Check for risk validation
                    if 'risk' not in source.lower():
                        self.add_issue('WARNING', name,
                                     "Position size calculation should consider risk")
                
                print(f"  ✓ {name} reviewed")
            
        except Exception as e:
            self.add_issue('CRITICAL', 'MoneyManagement', f"Failed to review: {e}")
    
    def review_trading_strategies(self):
        """Review trading strategies"""
        print("🔍 Reviewing Trading Strategies...")
        
        try:
            from strategies.trend_following import TrendFollowingStrategy
            from strategies.mean_reversion import MeanReversionStrategy
            from strategies.breakout import BreakoutStrategy
            
            strategies = [
                ('TrendFollowingStrategy', TrendFollowingStrategy),
                ('MeanReversionStrategy', MeanReversionStrategy),
                ('BreakoutStrategy', BreakoutStrategy)
            ]
            
            for name, strategy_class in strategies:
                print(f"  📈 Reviewing {name}...")
                
                # Check for required methods
                required_methods = ['get_ai_prompt', 'validate_signal', 'get_strategy_type']
                for method_name in required_methods:
                    if not hasattr(strategy_class, method_name):
                        self.add_issue('CRITICAL', name, f"Missing required method: {method_name}")
                
                # Check magic number assignment
                if hasattr(strategy_class, '__init__'):
                    source = inspect.getsource(strategy_class.__init__)
                    if 'magic_number' not in source:
                        self.add_issue('WARNING', name, "Should assign unique magic number")
                
                print(f"  ✓ {name} reviewed")
            
        except Exception as e:
            self.add_issue('CRITICAL', 'TradingStrategies', f"Failed to review: {e}")
    
    def review_mt5_integration(self):
        """Review MT5 integration"""
        print("🔍 Reviewing MT5 Integration...")
        
        try:
            from mt5_integration.mt5_client import MT5Client
            
            # Check critical methods
            critical_methods = ['login', 'place_order', 'get_market_data', 'get_positions']
            
            for method_name in critical_methods:
                if hasattr(MT5Client, method_name):
                    source = inspect.getsource(getattr(MT5Client, method_name))
                    
                    # Check for error handling
                    if 'try:' not in source or 'except' not in source:
                        self.add_issue('CRITICAL', 'MT5Client', 
                                     f"Method {method_name} lacks proper error handling")
                    
                    # Check for connection validation
                    if method_name != 'login' and 'current_account' not in source:
                        self.add_issue('WARNING', 'MT5Client',
                                     f"Method {method_name} should validate connection")
                
                else:
                    self.add_issue('CRITICAL', 'MT5Client', f"Missing method: {method_name}")
            
            print("  ✓ MT5 Client reviewed")
            
        except Exception as e:
            self.add_issue('CRITICAL', 'MT5Integration', f"Failed to review: {e}")
    
    def review_ai_integration(self):
        """Review AI integration"""
        print("🔍 Reviewing AI Integration...")
        
        try:
            from ai_integration.qwen_client import QwenClient
            from ai_integration.prompt_builder import PromptBuilder
            
            # Review QwenClient
            if hasattr(QwenClient, 'generate_trading_decision'):
                source = inspect.getsource(QwenClient.generate_trading_decision)
                
                # Check for timeout handling
                if 'timeout' not in source:
                    self.add_issue('WARNING', 'QwenClient', 
                                 "Should implement timeout for API calls")
                
                # Check for response validation
                if 'validate' not in source.lower() and 'parse' not in source.lower():
                    self.add_issue('WARNING', 'QwenClient',
                                 "Should validate API responses")
            
            # Review PromptBuilder
            if hasattr(PromptBuilder, 'build_trading_prompt'):
                source = inspect.getsource(PromptBuilder.build_trading_prompt)
                
                # Check for error handling in prompt building
                if 'try:' not in source or 'except' not in source:
                    self.add_issue('WARNING', 'PromptBuilder',
                                 "Should handle errors in prompt building")
            
            print("  ✓ AI Integration reviewed")
            
        except Exception as e:
            self.add_issue('CRITICAL', 'AIIntegration', f"Failed to review: {e}")
    
    def review_signal_generation(self):
        """Review signal generation logic"""
        print("🔍 Reviewing Signal Generation...")
        
        try:
            from signal_generation.signal_generator import SignalGenerator
            
            # Check critical methods
            critical_methods = ['generate_signals', '_execute_signal', '_should_generate_signal']
            
            for method_name in critical_methods:
                if hasattr(SignalGenerator, method_name):
                    source = inspect.getsource(getattr(SignalGenerator, method_name))
                    
                    # Check for risk management
                    if 'risk' not in source.lower() and method_name == '_execute_signal':
                        self.add_issue('WARNING', 'SignalGenerator',
                                     f"Method {method_name} should include risk checks")
                    
                    # Check for validation
                    if 'validate' not in source.lower() and method_name == '_execute_signal':
                        self.add_issue('WARNING', 'SignalGenerator',
                                     f"Method {method_name} should validate signals")
                
                else:
                    self.add_issue('CRITICAL', 'SignalGenerator', f"Missing method: {method_name}")
            
            print("  ✓ Signal Generation reviewed")
            
        except Exception as e:
            self.add_issue('CRITICAL', 'SignalGeneration', f"Failed to review: {e}")
    
    def review_trade_management(self):
        """Review trade management logic"""
        print("🔍 Reviewing Trade Management...")
        
        try:
            from trade_management.trade_manager import TradeManager
            
            # Check critical methods
            critical_methods = ['manage_trades', '_manage_account_trades', '_manage_single_position']
            
            for method_name in critical_methods:
                if hasattr(TradeManager, method_name):
                    source = inspect.getsource(getattr(TradeManager, method_name))
                    
                    # Check for error handling
                    if 'try:' not in source or 'except' not in source:
                        self.add_issue('WARNING', 'TradeManager',
                                     f"Method {method_name} should have error handling")
                    
                    # Check for position validation
                    if 'position' in method_name.lower() and 'validate' not in source.lower():
                        self.add_issue('INFO', 'TradeManager',
                                     f"Consider adding position validation in {method_name}")
                
                else:
                    self.add_issue('CRITICAL', 'TradeManager', f"Missing method: {method_name}")
            
            print("  ✓ Trade Management reviewed")
            
        except Exception as e:
            self.add_issue('CRITICAL', 'TradeManagement', f"Failed to review: {e}")
    
    def generate_report(self):
        """Generate comprehensive review report"""
        print("\n" + "="*80)
        print("📋 CODE LOGIC REVIEW REPORT")
        print("="*80)
        
        total_issues = len(self.issues) + len(self.warnings) + len(self.recommendations)
        
        print(f"Total Issues Found: {total_issues}")
        print(f"🔴 Critical Issues: {len(self.issues)}")
        print(f"🟡 Warnings: {len(self.warnings)}")
        print(f"🔵 Recommendations: {len(self.recommendations)}")
        
        if self.issues:
            print("\n🔴 CRITICAL ISSUES:")
            for issue in self.issues:
                print(f"  - {issue['component']}: {issue['description']}")
                if issue['recommendation']:
                    print(f"    💡 {issue['recommendation']}")
        
        if self.warnings:
            print("\n🟡 WARNINGS:")
            for warning in self.warnings:
                print(f"  - {warning['component']}: {warning['description']}")
                if warning['recommendation']:
                    print(f"    💡 {warning['recommendation']}")
        
        if self.recommendations:
            print("\n🔵 RECOMMENDATIONS:")
            for rec in self.recommendations:
                print(f"  - {rec['component']}: {rec['description']}")
                if rec['recommendation']:
                    print(f"    💡 {rec['recommendation']}")
        
        # Overall assessment
        if len(self.issues) == 0:
            if len(self.warnings) <= 3:
                print("\n✅ OVERALL ASSESSMENT: GOOD - Ready for production with minor improvements")
                status = "PRODUCTION_READY"
            else:
                print("\n⚠️  OVERALL ASSESSMENT: FAIR - Address warnings before production")
                status = "NEEDS_IMPROVEMENT"
        else:
            print("\n❌ OVERALL ASSESSMENT: POOR - Fix critical issues before production")
            status = "NOT_READY"
        
        return status

def main():
    """Main review function"""
    print("🔧 Starting Comprehensive Code Logic Review")
    print("=" * 60)
    
    reviewer = CodeLogicReviewer()
    
    # Review all components
    reviewer.review_account_management()
    reviewer.review_money_management()
    reviewer.review_trading_strategies()
    reviewer.review_mt5_integration()
    reviewer.review_ai_integration()
    reviewer.review_signal_generation()
    reviewer.review_trade_management()
    
    # Generate report
    status = reviewer.generate_report()
    
    return status

if __name__ == "__main__":
    main()
