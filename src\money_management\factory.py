"""
Money Management Strategy Factory
"""

from typing import Dict, Any
from money_management.base_strategy import BaseMoneyManagement, MoneyManagementType
from money_management.fixed_volume import FixedVolumeStrategy
from money_management.percent_risk import PercentRiskStrategy
from money_management.martingale import MartingaleStrategy
from money_management.anti_martingale import AntiMartingaleStrategy

class MoneyManagementFactory:
    """Factory for creating money management strategy instances"""
    
    _strategies = {
        MoneyManagementType.FIXED_VOLUME: FixedVolumeStrategy,
        MoneyManagementType.PERCENT_RISK: PercentRiskStrategy,
        MoneyManagementType.MARTINGALE: MartingaleStrategy,
        MoneyManagementType.ANTI_MARTINGALE: AntiMartingaleStrategy,
    }
    
    @classmethod
    def create_strategy(
        cls, 
        strategy_type: MoneyManagementType, 
        config: Dict[str, Any]
    ) -> BaseMoneyManagement:
        """Create a money management strategy instance"""
        
        if strategy_type not in cls._strategies:
            raise ValueError(f"Unknown money management strategy: {strategy_type}")
        
        strategy_class = cls._strategies[strategy_type]
        return strategy_class(config)
    
    @classmethod
    def get_available_strategies(cls) -> list:
        """Get list of available strategy types"""
        return list(cls._strategies.keys())
    
    @classmethod
    def get_default_config(cls, strategy_type: MoneyManagementType) -> Dict[str, Any]:
        """Get default configuration for a strategy type"""
        
        default_configs = {
            MoneyManagementType.FIXED_VOLUME: {
                'fixed_volume': 0.01,
                'max_daily_trades': 5,
            },
            MoneyManagementType.PERCENT_RISK: {
                'risk_percent': 2.0,
                'default_volume': 0.01,
                'max_daily_risk_percent': 10.0,
            },
            MoneyManagementType.MARTINGALE: {
                'base_volume': 0.01,
                'max_multiplier': 8,
                'max_consecutive_losses': 4,
            },
            MoneyManagementType.ANTI_MARTINGALE: {
                'base_volume': 0.01,
                'max_multiplier': 4,
                'win_multiplier_increment': 0.5,
            },
        }
        
        return default_configs.get(strategy_type, {})
