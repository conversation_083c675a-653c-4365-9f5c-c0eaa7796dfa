"""
Breakout Trading Strategy
"""

from typing import Dict, Any, List
from strategies.base_strategy import BaseStrategy, StrategyType, MarketData, TradingSignal

class BreakoutStrategy(BaseStrategy):
    """
    Breakout Strategy
    Trades breakouts from consolidation patterns and key levels
    """
    
    def get_strategy_type(self) -> StrategyType:
        return StrategyType.BREAKOUT
    
    def get_strategy_name(self) -> str:
        return "Momentum Breakout"
    
    def get_ai_prompt(
        self, 
        market_data: MarketData,
        trade_history: List[Dict[str, Any]],
        account_info: Dict[str, Any]
    ) -> str:
        """Get AI prompt for breakout strategy"""
        
        recent_trades = trade_history[-12:] if len(trade_history) > 12 else trade_history
        win_rate = self._calculate_win_rate(recent_trades)
        false_breakout_rate = self._calculate_false_breakout_rate(recent_trades)
        
        prompt = f"""
You are executing a BREAKOUT trading strategy for {market_data.symbol} on {market_data.timeframe} timeframe.

STRATEGY OVERVIEW:
- Strategy Type: Breakout Trading
- Magic Number: {self.magic_number}
- Focus: Trade momentum breakouts from consolidation
- Timeframe: {market_data.timeframe}
- Symbol: {market_data.symbol}

MARKET DATA ANALYSIS:
- Current Price: {market_data.current_price}
- Spread: {market_data.spread} pips
- Volume: {market_data.volume}
- Volatility: {market_data.volatility:.5f}

RECENT PERFORMANCE:
- Win Rate: {win_rate:.1f}%
- False Breakout Rate: {false_breakout_rate:.1f}%
- Total Recent Trades: {len(recent_trades)}

BREAKOUT TRADING PRINCIPLES:
1. Trade the break of significant support/resistance levels
2. Volume confirmation is crucial for valid breakouts
3. Wait for price to close beyond the level, not just touch
4. False breakouts are common - use proper confirmation
5. Best during high volatility and volume periods

TECHNICAL ANALYSIS FOCUS:
- Horizontal support/resistance levels
- Trend lines and channel breakouts
- Chart patterns (triangles, rectangles, flags)
- Volume analysis for breakout confirmation
- Momentum indicators for strength confirmation

ENTRY CRITERIA:
1. Clear consolidation pattern identified
2. Price breaks key level with strong momentum
3. Volume spike confirming the breakout (2x average volume)
4. Candle closes beyond the breakout level
5. No immediate resistance in breakout direction

BREAKOUT CONFIRMATION SIGNALS:
- Strong volume on breakout candle
- Follow-through in next 1-3 candles
- No immediate return to broken level
- Momentum indicators supporting direction
- Time of day favorable for volatility

EXIT CRITERIA:
1. Target: Measured move from pattern height
2. Stop loss: Back inside the broken pattern
3. Trail stops once in profit
4. Volume drying up (momentum exhaustion)
5. Reversal patterns at target levels

RISK MANAGEMENT:
- Stop loss just inside the broken level
- Position size based on pattern size
- Avoid breakouts during low volume periods
- Maximum 2 breakout trades per session

PATTERN TYPES TO WATCH:
- Horizontal rectangles/ranges
- Ascending/descending triangles
- Symmetrical triangles
- Flag and pennant patterns
- Head and shoulders patterns

MARKET CONDITIONS TO FAVOR:
- High volatility periods
- After consolidation phases
- During active trading sessions
- Around key economic releases

MARKET CONDITIONS TO AVOID:
- Low volume periods
- End of trading sessions
- Holidays and thin markets
- Immediately after major news

Based on the 200 candles of market data provided, analyze:
1. Current consolidation patterns and key levels
2. Volume patterns and accumulation/distribution
3. Potential breakout levels and targets
4. Pattern maturity and breakout probability
5. Volume confirmation requirements
6. Optimal timing for breakout trades
7. False breakout risk assessment

Provide specific trading signals with:
- Action: BUY/SELL/HOLD/CLOSE with reasoning
- Entry price (on breakout confirmation)
- Stop loss (inside broken pattern)
- Take profit targets (measured moves)
- Volume confirmation requirements
- Confidence level (0-100%)
- Risk assessment (LOW/MEDIUM/HIGH)

Remember: Patience is key for breakout trading. Wait for proper confirmation.
False breakouts are expensive - always wait for volume and follow-through confirmation.
"""
        return prompt
    
    def validate_signal(self, signal: TradingSignal, market_data: MarketData) -> bool:
        """Validate breakout signal"""
        
        # Must have stop loss for breakout trading
        if not signal.stop_loss:
            return False
        
        # High confidence required for breakouts
        if signal.confidence < 0.75:
            return False
        
        # Volume should be above average for valid breakouts
        avg_volume = self.config.get('average_volume', 1000)
        if market_data.volume < avg_volume * 1.5:
            return False
        
        # Check spread conditions
        if market_data.spread > self.config.get('max_spread', 2.5):
            return False
        
        return True
    
    def _calculate_win_rate(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate win rate from trade history"""
        if not trades:
            return 0.0
        
        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        return (winning_trades / len(trades)) * 100
    
    def _calculate_false_breakout_rate(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate false breakout rate"""
        if not trades:
            return 0.0
        
        false_breakouts = sum(1 for trade in trades 
                            if trade.get('exit_reason') == 'false_breakout')
        return (false_breakouts / len(trades)) * 100
