#!/usr/bin/env python3
"""
AI Prompt and Response Validation Tests
Validates that all AI prompts are logical, comprehensive, and can handle all scenarios
"""

import unittest
import sys
import os
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from strategies.trend_following import TrendFollowingStrategy
from strategies.mean_reversion import MeanReversionStrategy
from strategies.breakout import BreakoutStrategy
from strategies.base_strategy import MarketData, StrategyType
from money_management.percent_risk import PercentRiskStrategy
from money_management.fixed_volume import FixedVolumeStrategy
from money_management.martingale import MartingaleStrategy
from money_management.anti_martingale import AntiMartingaleStrategy
from money_management.base_strategy import AccountInfo, MoneyManagementType
from ai_integration.prompt_builder import PromptBuilder
from ai_integration.qwen_client import QwenClient

class TestAIPromptValidation(unittest.TestCase):
    """Test AI prompt generation and validation"""
    
    def setUp(self):
        """Set up test environment"""
        self.market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[
                {'time': '2025-08-01 10:00:00', 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000},
                {'time': '2025-08-01 11:00:00', 'open': 1.1010, 'high': 1.1030, 'low': 1.0990, 'close': 1.1020, 'volume': 1200},
                {'time': '2025-08-01 12:00:00', 'open': 1.1020, 'high': 1.1040, 'low': 1.1000, 'close': 1.1030, 'volume': 1100},
            ],
            current_price=1.1030,
            spread=1.5,
            volume=1100,
            volatility=0.0015
        )
        
        self.account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        self.trade_history = [
            {'profit': 100.0, 'magic_number': 12345, 'symbol': 'EURUSD', 'close_time': '2025-08-01 09:00:00'},
            {'profit': -50.0, 'magic_number': 12345, 'symbol': 'EURUSD', 'close_time': '2025-08-01 08:00:00'},
            {'profit': 75.0, 'magic_number': 12345, 'symbol': 'EURUSD', 'close_time': '2025-08-01 07:00:00'},
        ]
        
        self.account_info_dict = {
            'balance': 10000.0,
            'equity': 10000.0,
            'currency': 'USD'
        }
    
    def test_trend_following_prompt_comprehensive(self):
        """Test trend following strategy prompt comprehensiveness"""
        strategy = TrendFollowingStrategy({'magic_number': 12345})
        
        prompt = strategy.get_ai_prompt(self.market_data, self.trade_history, self.account_info_dict)
        
        # Check prompt structure and content
        self.assertIsInstance(prompt, str)
        self.assertGreater(len(prompt), 1000)  # Should be comprehensive
        
        # Check for essential trading concepts
        essential_concepts = [
            "TREND FOLLOWING", "trend direction", "moving averages", "breakout",
            "support", "resistance", "stop loss", "take profit", "risk management",
            "EURUSD", "H1", "volume", "volatility", "entry", "exit"
        ]
        
        for concept in essential_concepts:
            self.assertIn(concept.lower(), prompt.lower(), f"Missing concept: {concept}")
        
        # Check for risk management guidelines
        risk_concepts = ["stop loss", "risk", "position size"]
        for concept in risk_concepts:
            self.assertIn(concept.lower(), prompt.lower(), f"Missing risk concept: {concept}")
        
        # Check for market analysis requirements
        analysis_concepts = ["technical analysis", "price action", "momentum"]
        for concept in analysis_concepts:
            self.assertIn(concept.lower(), prompt.lower(), f"Missing analysis concept: {concept}")
    
    def test_mean_reversion_prompt_comprehensive(self):
        """Test mean reversion strategy prompt comprehensiveness"""
        strategy = MeanReversionStrategy({'magic_number': 12346})
        
        prompt = strategy.get_ai_prompt(self.market_data, self.trade_history, self.account_info_dict)
        
        # Check for mean reversion specific concepts
        mr_concepts = [
            "MEAN REVERSION", "overbought", "oversold", "reversion", "extreme",
            "oscillator", "mean", "consolidation"
        ]
        
        for concept in mr_concepts:
            self.assertIn(concept.lower(), prompt.lower(), f"Missing MR concept: {concept}")
    
    def test_breakout_prompt_comprehensive(self):
        """Test breakout strategy prompt comprehensiveness"""
        strategy = BreakoutStrategy({'magic_number': 12347})
        
        prompt = strategy.get_ai_prompt(self.market_data, self.trade_history, self.account_info_dict)
        
        # Check for breakout specific concepts
        breakout_concepts = [
            "BREAKOUT", "consolidation", "pattern", "volume", "confirmation",
            "false breakout", "momentum", "key levels"
        ]
        
        for concept in breakout_concepts:
            self.assertIn(concept.lower(), prompt.lower(), f"Missing breakout concept: {concept}")
    
    def test_percent_risk_prompt_comprehensive(self):
        """Test percent risk money management prompt comprehensiveness"""
        strategy = PercentRiskStrategy({'risk_percent': 2.0})
        
        prompt = strategy.get_ai_prompt(self.account_info, self.trade_history)
        
        # Check for money management concepts
        mm_concepts = [
            "PERCENT RISK", "position size", "stop loss",
            "account balance", "risk management"
        ]
        
        for concept in mm_concepts:
            self.assertIn(concept.lower(), prompt.lower(), f"Missing MM concept: {concept}")
        
        # Check for specific risk percentage
        self.assertIn("2.0", prompt)
    
    def test_martingale_prompt_comprehensive(self):
        """Test martingale money management prompt comprehensiveness"""
        strategy = MartingaleStrategy({'base_volume': 0.01, 'max_multiplier': 8})
        
        prompt = strategy.get_ai_prompt(self.account_info, self.trade_history)
        
        # Check for martingale concepts
        martingale_concepts = [
            "MARTINGALE", "consecutive losses", "multiplier",
            "risk", "sequence"
        ]
        
        for concept in martingale_concepts:
            self.assertIn(concept.lower(), prompt.lower(), f"Missing martingale concept: {concept}")
    
    def test_anti_martingale_prompt_comprehensive(self):
        """Test anti-martingale money management prompt comprehensiveness"""
        strategy = AntiMartingaleStrategy({'base_volume': 0.01, 'max_multiplier': 4})
        
        prompt = strategy.get_ai_prompt(self.account_info, self.trade_history)
        
        # Check for anti-martingale concepts
        anti_martingale_concepts = [
            "ANTI-MARTINGALE", "consecutive wins", "increase", "winning streak",
            "profits", "momentum"
        ]
        
        for concept in anti_martingale_concepts:
            self.assertIn(concept.lower(), prompt.lower(), f"Missing anti-martingale concept: {concept}")
    
    def test_prompt_builder_integration(self):
        """Test prompt builder integration with all components"""
        prompt_builder = PromptBuilder()
        
        # Test with trend following + percent risk
        strategy = TrendFollowingStrategy({'magic_number': 12345})
        money_management = PercentRiskStrategy({'risk_percent': 2.0})
        
        comprehensive_prompt = prompt_builder.build_trading_prompt(
            strategy=strategy,
            money_management=money_management,
            market_data=self.market_data,
            trade_history=self.trade_history,
            account_info=self.account_info
        )
        
        # Check comprehensive prompt structure
        self.assertGreater(len(comprehensive_prompt), 2000)  # Should be very comprehensive
        
        # Check for integration elements
        integration_elements = [
            "INTEGRATION INSTRUCTIONS", "RESPONSE REQUIREMENTS", "JSON format",
            "strategy requirements", "money management constraints",
            "CURRENT MARKET CONDITIONS", "TECHNICAL ANALYSIS REQUIREMENTS"
        ]
        
        for element in integration_elements:
            self.assertIn(element, comprehensive_prompt, f"Missing integration element: {element}")
        
        # Check for required JSON fields
        json_fields = [
            '"action"', '"confidence"', '"entry_price"', '"stop_loss"',
            '"take_profit"', '"reasoning"', '"risk_level"', '"market_analysis"'
        ]
        
        for field in json_fields:
            self.assertIn(field, comprehensive_prompt, f"Missing JSON field: {field}")
    
    def test_prompt_error_handling(self):
        """Test prompt generation error handling"""
        prompt_builder = PromptBuilder()
        
        # Test with failing strategy
        failing_strategy = Mock()
        failing_strategy.get_ai_prompt.side_effect = Exception("Strategy error")
        
        money_management = PercentRiskStrategy({'risk_percent': 2.0})
        
        # Should not crash and should provide fallback
        prompt = prompt_builder.build_trading_prompt(
            strategy=failing_strategy,
            money_management=money_management,
            market_data=self.market_data,
            trade_history=self.trade_history,
            account_info=self.account_info
        )
        
        self.assertIsInstance(prompt, str)
        self.assertIn("EMERGENCY", prompt)
        self.assertIn("error", prompt.lower())

class TestAIResponseValidation(unittest.TestCase):
    """Test AI response parsing and validation"""
    
    def setUp(self):
        """Set up test environment"""
        self.valid_responses = [
            {
                "action": "BUY",
                "confidence": 0.85,
                "entry_price": 1.1030,
                "stop_loss": 1.1000,
                "take_profit": 1.1080,
                "reasoning": "Strong uptrend with volume confirmation",
                "risk_level": "MEDIUM",
                "market_analysis": "Bullish momentum building",
                "strategy_alignment": "Perfect trend following setup",
                "risk_assessment": "2% risk within limits"
            },
            {
                "action": "SELL",
                "confidence": 0.75,
                "entry_price": 1.1020,
                "stop_loss": 1.1050,
                "take_profit": 1.0980,
                "take_profit_levels": [
                    {"price": 1.1000, "volume_percent": 50},
                    {"price": 1.0980, "volume_percent": 50}
                ],
                "reasoning": "Overbought conditions with divergence",
                "risk_level": "HIGH"
            },
            {
                "action": "HOLD",
                "confidence": 0.3,
                "reasoning": "Unclear market direction, waiting for confirmation",
                "risk_level": "LOW"
            }
        ]
        
        self.invalid_responses = [
            {"action": "INVALID_ACTION"},  # Invalid action
            {"action": "BUY", "confidence": 1.5},  # Invalid confidence
            {"action": "BUY", "confidence": 0.8, "entry_price": "invalid"},  # Invalid price
            {},  # Empty response
            {"action": "BUY"},  # Missing required fields
        ]
    
    @patch.dict(os.environ, {'QWEN_API_KEY': 'test_key'})
    def test_valid_response_parsing(self):
        """Test parsing of valid AI responses"""
        client = QwenClient()
        
        for response in self.valid_responses:
            # Test that valid responses are parsed correctly
            parsed = client._parse_ai_response(
                {'choices': [{'message': {'content': json.dumps(response)}}]},
                {}
            )
            
            self.assertIsInstance(parsed, dict)
            self.assertIn('action', parsed)
            self.assertEqual(parsed['action'], response['action'])
            
            if 'confidence' in response:
                self.assertEqual(parsed['confidence'], response['confidence'])
    
    @patch.dict(os.environ, {'QWEN_API_KEY': 'test_key'})
    def test_invalid_response_handling(self):
        """Test handling of invalid AI responses"""
        client = QwenClient()
        
        for invalid_response in self.invalid_responses:
            # Test that invalid responses are handled gracefully
            parsed = client._parse_ai_response(
                {'choices': [{'message': {'content': json.dumps(invalid_response)}}]},
                {}
            )
            
            self.assertIsInstance(parsed, dict)
            # Should default to HOLD for invalid responses
            self.assertEqual(parsed['action'], 'HOLD')
            self.assertTrue('error' in parsed['reasoning'].lower() or 'failed' in parsed['reasoning'].lower())
    
    def test_response_field_validation(self):
        """Test validation of specific response fields"""
        
        # Test action validation
        valid_actions = ['BUY', 'SELL', 'HOLD', 'CLOSE']
        for action in valid_actions:
            self.assertIn(action, valid_actions)
        
        # Test confidence validation
        valid_confidences = [0.0, 0.5, 1.0]
        invalid_confidences = [-0.1, 1.1, 'invalid']
        
        for conf in valid_confidences:
            self.assertGreaterEqual(conf, 0.0)
            self.assertLessEqual(conf, 1.0)
        
        # Test risk level validation
        valid_risk_levels = ['LOW', 'MEDIUM', 'HIGH']
        for risk_level in valid_risk_levels:
            self.assertIn(risk_level, valid_risk_levels)

def run_ai_validation_tests():
    """Run all AI validation tests"""
    unittest.main(verbosity=2)

if __name__ == "__main__":
    run_ai_validation_tests()
