#!/usr/bin/env python3
"""
End-to-end test to verify volume calculations work correctly
"""

import sys
import json
sys.path.append('src')

from account_management.account_manager import AccountManager
from money_management.factory import MoneyManagementFactory
from money_management.base_strategy import AccountInfo

def test_account_config():
    """Test the actual account configuration"""
    print("=" * 60)
    print("TESTING ACTUAL ACCOUNT CONFIGURATION")
    print("=" * 60)
    
    # Load the actual account config
    with open('config/accounts.json', 'r') as f:
        config = json.load(f)
    
    account_config = config['accounts'][0]
    print(f"Account ID: {account_config['account_id']}")
    print(f"Strategy: {account_config['strategy']}")
    print(f"Money Management: {account_config['money_management']}")
    print(f"Symbol: {account_config['symbols'][0]['symbol']}")
    print(f"Timeframe: {account_config['symbols'][0]['timeframe']}")
    print()
    
    # Print money management settings
    mm_settings = account_config['money_management_settings']
    print("Money Management Settings:")
    for key, value in mm_settings.items():
        print(f"  {key}: {value}")
    print()
    
    return account_config

def test_money_management_factory():
    """Test money management factory with actual config"""
    print("=" * 60)
    print("TESTING MONEY MANAGEMENT FACTORY")
    print("=" * 60)
    
    account_config = test_account_config()
    
    # Create money management strategy
    factory = MoneyManagementFactory()
    mm_settings = account_config['money_management_settings']
    
    try:
        from money_management.base_strategy import MoneyManagementType
        mm_type = MoneyManagementType.PERCENT_RISK
        strategy = factory.create_strategy(mm_type, mm_settings)
        
        if strategy:
            print("✅ Money management strategy created successfully")
            print(f"Strategy type: {strategy.strategy_type}")
            print(f"Config: {strategy.config}")
        else:
            print("❌ Failed to create money management strategy")
            return None
            
    except Exception as e:
        print(f"❌ Error creating strategy: {e}")
        return None
    
    return strategy

def test_volume_calculation():
    """Test actual volume calculation"""
    print("\n" + "=" * 60)
    print("TESTING VOLUME CALCULATION")
    print("=" * 60)
    
    strategy = test_money_management_factory()
    if not strategy:
        return
    
    # Simulate current account info (approximately $80 balance)
    account_info = AccountInfo(
        balance=80.0,
        equity=80.0,
        margin=0.0,
        free_margin=40.0,
        margin_level=1000.0,
        currency="USD",
        leverage=500
    )
    
    # Market data for EURUSD
    market_data = {
        "pip_value": 10.0,  # $10 per pip per lot for EURUSD
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    # Test scenarios
    test_cases = [
        {"name": "Small SL (15 pips)", "entry": 1.0850, "sl": 1.0835},
        {"name": "Medium SL (25 pips)", "entry": 1.0850, "sl": 1.0825},
        {"name": "Large SL (35 pips)", "entry": 1.0850, "sl": 1.0815},
    ]
    
    print(f"Account Balance: ${account_info.balance}")
    print(f"Risk Percent: {strategy.config.get('risk_percent', 'N/A')}%")
    print(f"Expected Risk Amount: ${account_info.balance * (strategy.config.get('risk_percent', 0)/100):.2f}")
    print()
    
    for case in test_cases:
        print(f"{case['name']}:")
        
        try:
            trade_params = strategy.calculate_position_size(
                account_info=account_info,
                symbol="EURUSD",
                entry_price=case['entry'],
                stop_loss=case['sl'],
                trade_history=[],
                market_data=market_data
            )
            
            pip_diff = abs(case['entry'] - case['sl']) / market_data['pip_size']
            
            print(f"  Entry: {case['entry']}, SL: {case['sl']}")
            print(f"  Stop Loss: {pip_diff:.0f} pips")
            print(f"  Volume: {trade_params.volume:.2f} lots")
            print(f"  Risk Amount: ${trade_params.risk_amount:.2f}")
            print(f"  Risk %: {(trade_params.risk_amount/account_info.balance)*100:.2f}%")
            print(f"  Confidence: {trade_params.confidence_level:.2f}")
            
            # Validate the calculation
            expected_risk = pip_diff * market_data['pip_value'] * trade_params.volume
            print(f"  Validation: {pip_diff:.0f} pips × ${market_data['pip_value']} × {trade_params.volume:.2f} = ${expected_risk:.2f}")
            
            if abs(expected_risk - trade_params.risk_amount) < 0.01:
                print("  ✅ Calculation correct")
            else:
                print("  ❌ Calculation error")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
        
        print()

def test_with_different_risk_percents():
    """Test how different risk percentages affect volume"""
    print("=" * 60)
    print("TESTING DIFFERENT RISK PERCENTAGES")
    print("=" * 60)
    
    account_info = AccountInfo(80.0, 80.0, 0.0, 40.0, 1000.0, "USD", 500)
    market_data = {
        "pip_value": 10.0,
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    # Test different risk percentages
    risk_percentages = [0.25, 0.5, 1.0, 2.0]
    
    for risk_pct in risk_percentages:
        print(f"\nRisk Percentage: {risk_pct}%")
        print("-" * 30)
        
        from money_management.percent_risk import PercentRiskStrategy
        strategy = PercentRiskStrategy({"risk_percent": risk_pct})
        
        # Test with 25 pip stop loss
        trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=1.0850,
            stop_loss=1.0825,  # 25 pips
            trade_history=[],
            market_data=market_data
        )
        
        expected_risk = account_info.balance * (risk_pct / 100)
        
        print(f"  Expected Risk: ${expected_risk:.2f}")
        print(f"  Volume: {trade_params.volume:.2f} lots")
        print(f"  Actual Risk: ${trade_params.risk_amount:.2f}")
        print(f"  Risk Multiplier: {trade_params.risk_amount / expected_risk:.1f}x")

if __name__ == "__main__":
    print("END-TO-END VOLUME CALCULATION TEST")
    print("=" * 60)
    
    test_volume_calculation()
    test_with_different_risk_percents()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("- With $80 account and 0.5% risk, expected risk is $0.40")
    print("- Due to 0.01 minimum volume, actual risk will be $1.50-$3.50")
    print("- This is 3.75x to 8.75x the intended risk")
    print("- This is normal for small accounts with minimum volume constraints")
    print("=" * 60)
