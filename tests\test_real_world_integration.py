#!/usr/bin/env python3
"""
Real-World Integration Tests for Trading System
Tests that simulate actual trading scenarios including signal execution,
trade lifecycle management, pending orders, and closed trade retrieval.
"""

import unittest
import asyncio
import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
import time

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from account_management.account_manager import AccountManager
from account_management.models import TradingAccount
from signal_generation.signal_generator import SignalGenerator
from trade_management.trade_manager import TradeManager
from mt5_integration.mt5_client import MT5Client
from ai_integration.qwen_client import QwenClient
from logging_system.logger import trading_logger

class TestRealWorldSignalExecution(unittest.IsolatedAsyncioTestCase):
    """Test real-world signal execution scenarios"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.test_config_file = os.path.join(self.test_dir, "test_accounts.json")
        
        # Create realistic test configuration
        self.test_config = {
            "accounts": [
                {
                    "account_id": "real_test_account",
                    "account_number": ********,
                    "password": "test_password",
                    "server": "RoboForex-ECN",
                    "strategy": "trend_following",
                    "money_management": "percent_risk",
                    "symbols": [
                        {"symbol": "EURUSD", "timeframe": "H1"},
                        {"symbol": "GBPUSD", "timeframe": "H4"}
                    ],
                    "money_management_settings": {
                        "risk_percent": 2.0
                    },
                    "max_daily_trades": 5,
                    "max_concurrent_positions": 3,
                    "trading_enabled": True
                }
            ]
        }
        
        with open(self.test_config_file, 'w') as f:
            json.dump(self.test_config, f)
        
        self.account_manager = AccountManager(config_path=self.test_config_file)
        self.account_manager.load_accounts()
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir)
    
    @patch('mt5_integration.mt5_client.mt5')
    @patch.dict(os.environ, {'QWEN_API_KEY': 'test_key'})
    async def test_complete_signal_lifecycle(self, mock_mt5):
        """Test complete signal generation and execution lifecycle"""
        
        # Mock MT5 responses
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = True
        mock_mt5.account_info.return_value = Mock(
            login=********,
            balance=10000.0,
            equity=10000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        # Mock market data
        mock_rates = [
            (datetime.now().timestamp(), 1.1000, 1.1020, 1.0980, 1.1010, 0, 1000, 0),
            (datetime.now().timestamp(), 1.1010, 1.1030, 1.0990, 1.1020, 0, 1200, 0),
            (datetime.now().timestamp(), 1.1020, 1.1040, 1.1000, 1.1030, 0, 1100, 0),
        ]
        mock_mt5.copy_rates_from_pos.return_value = mock_rates
        mock_mt5.symbol_info_tick.return_value = Mock(bid=1.1028, ask=1.1030)
        mock_mt5.symbol_info.return_value = Mock(
            volume_min=0.01,
            volume_max=100.0,
            point=0.00001
        )
        
        # Mock successful order placement
        mock_mt5.order_send.return_value = Mock(
            retcode=10009,  # TRADE_RETCODE_DONE
            order=12345,
            deal=67890
        )
        
        # Mock trade history (empty initially)
        mock_mt5.history_deals_get.return_value = []
        mock_mt5.positions_get.return_value = []
        
        # Initialize signal generator
        signal_generator = SignalGenerator(self.account_manager)
        
        # Mock AI response for signal generation
        with patch('ai_integration.qwen_client.aiohttp.ClientSession.post') as mock_post:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {
                'choices': [{
                    'message': {
                        'content': json.dumps({
                            "action": "BUY",
                            "confidence": 0.85,
                            "entry_price": 1.1030,
                            "stop_loss": 1.1000,
                            "take_profit": 1.1080,
                            "reasoning": "Strong uptrend confirmed with volume support",
                            "risk_level": "MEDIUM",
                            "market_analysis": "Bullish momentum building",
                            "strategy_alignment": "Perfect trend following setup",
                            "risk_assessment": "2% risk well within limits"
                        })
                    }
                }]
            }
            mock_post.return_value.__aenter__.return_value = mock_response
            
            # Generate signal
            await signal_generator.generate_signals()
            
            # Verify MT5 order was placed
            mock_mt5.order_send.assert_called()
            
            # Verify logging occurred
            # Note: In real implementation, we'd check log files
    
    @patch('mt5_integration.mt5_client.mt5')
    async def test_multiple_take_profit_execution(self, mock_mt5):
        """Test execution of signals with multiple take profit levels"""
        
        # Setup MT5 mocks
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = True
        mock_mt5.account_info.return_value = Mock(
            login=********,
            balance=10000.0,
            equity=10000.0,
            margin_level=1000.0
        )
        
        # Mock successful order placements
        mock_mt5.order_send.return_value = Mock(
            retcode=10009,
            order=12345,
            deal=67890
        )
        
        signal_generator = SignalGenerator(self.account_manager)
        
        # Test signal with multiple TP levels
        test_signal = {
            "action": "BUY",
            "confidence": 0.8,
            "entry_price": 1.1030,
            "stop_loss": 1.1000,
            "take_profit_levels": [
                {"price": 1.1060, "volume_percent": 50},
                {"price": 1.1080, "volume_percent": 30},
                {"price": 1.1100, "volume_percent": 20}
            ],
            "reasoning": "Multi-level profit taking strategy"
        }
        
        # Execute signal
        account = list(self.account_manager.accounts.values())[0]
        await signal_generator._execute_signal(
            account.__dict__,
            "EURUSD",
            test_signal,
            Mock(),
            Mock()
        )
        
        # Should place multiple orders (1 main + 3 TP orders)
        self.assertGreaterEqual(mock_mt5.order_send.call_count, 3)
    
    @patch('mt5_integration.mt5_client.mt5')
    async def test_pending_order_management(self, mock_mt5):
        """Test pending order placement and management"""
        
        # Setup MT5 mocks
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = True
        
        # Mock pending orders
        mock_pending_orders = [
            Mock(
                ticket=11111,
                symbol="EURUSD",
                type=2,  # BUY_LIMIT
                volume=0.1,
                price_open=1.1000,
                sl=1.0950,
                tp=1.1050,
                magic=12345
            ),
            Mock(
                ticket=22222,
                symbol="GBPUSD",
                type=3,  # SELL_LIMIT
                volume=0.05,
                price_open=1.2500,
                sl=1.2550,
                tp=1.2450,
                magic=12345
            )
        ]
        mock_mt5.orders_get.return_value = mock_pending_orders
        
        # Mock order cancellation
        mock_mt5.order_send.return_value = Mock(retcode=10009)
        
        mt5_client = MT5Client()
        
        # Test getting pending orders
        pending_orders = mt5_client.get_pending_orders()
        self.assertEqual(len(pending_orders), 2)
        
        # Test order cancellation
        result = mt5_client.cancel_order(11111)
        self.assertTrue(result)
        mock_mt5.order_send.assert_called()
    
    @patch('mt5_integration.mt5_client.mt5')
    async def test_closed_trade_retrieval(self, mock_mt5):
        """Test retrieval and analysis of closed trades"""
        
        # Setup MT5 mocks
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = True
        
        # Mock closed trades history
        mock_deals = [
            Mock(
                ticket=100001,
                order=12345,
                time=int(datetime.now().timestamp()),
                symbol="EURUSD",
                type=0,  # BUY
                volume=0.1,
                price=1.1030,
                profit=50.0,
                magic=12345,
                comment="tp"
            ),
            Mock(
                ticket=100002,
                order=12346,
                time=int((datetime.now() - timedelta(hours=1)).timestamp()),
                symbol="GBPUSD",
                type=1,  # SELL
                volume=0.05,
                price=1.2500,
                profit=-25.0,
                magic=12345,
                comment="sl"
            ),
            Mock(
                ticket=100003,
                order=12347,
                time=int((datetime.now() - timedelta(hours=2)).timestamp()),
                symbol="EURUSD",
                type=0,  # BUY
                volume=0.1,
                price=1.1000,
                profit=75.0,
                magic=12345,
                comment="manual"
            )
        ]
        mock_mt5.history_deals_get.return_value = mock_deals
        
        mt5_client = MT5Client()
        
        # Test trade history retrieval
        trade_history = mt5_client.get_trade_history(days=7)
        
        self.assertEqual(len(trade_history), 3)
        
        # Verify trade data structure
        for trade in trade_history:
            self.assertIn('ticket', trade)
            self.assertIn('symbol', trade)
            self.assertIn('profit', trade)
            self.assertIn('magic_number', trade)
        
        # Test filtering by magic number
        strategy_trades = [t for t in trade_history if t['magic_number'] == 12345]
        self.assertEqual(len(strategy_trades), 3)
        
        # Test profit calculation
        total_profit = sum(t['profit'] for t in strategy_trades)
        self.assertEqual(total_profit, 100.0)  # 50 - 25 + 75
    
    async def test_trade_lifecycle_with_ai_management(self):
        """Test complete trade lifecycle with AI-driven management"""
        
        # This test simulates:
        # 1. Signal generation
        # 2. Trade execution
        # 3. Position monitoring
        # 4. AI-driven management decisions
        # 5. Trade closure
        
        with patch('mt5_integration.mt5_client.mt5') as mock_mt5:
            # Setup initial mocks
            mock_mt5.initialize.return_value = True
            mock_mt5.login.return_value = True
            mock_mt5.account_info.return_value = Mock(
                balance=10000.0,
                equity=10000.0,
                margin_level=1000.0
            )
            
            # Mock open position
            mock_position = Mock(
                ticket=12345,
                symbol="EURUSD",
                type=0,  # BUY
                volume=0.1,
                price_open=1.1030,
                price_current=1.1050,
                sl=1.1000,
                tp=1.1080,
                profit=20.0,
                magic=12345
            )
            mock_mt5.positions_get.return_value = [mock_position]
            
            # Mock market data for management
            mock_mt5.copy_rates_from_pos.return_value = [
                (datetime.now().timestamp(), 1.1050, 1.1060, 1.1040, 1.1055, 0, 1000, 0)
            ]
            mock_mt5.symbol_info_tick.return_value = Mock(bid=1.1053, ask=1.1055)
            
            # Initialize trade manager
            trade_manager = TradeManager(self.account_manager)
            
            # Mock AI management decision
            with patch('ai_integration.qwen_client.aiohttp.ClientSession.post') as mock_post:
                mock_response = AsyncMock()
                mock_response.status = 200
                mock_response.json.return_value = {
                    'choices': [{
                        'message': {
                            'content': json.dumps({
                                "action": "MODIFY",
                                "confidence": 0.7,
                                "stop_loss": 1.1020,  # Trail stop loss
                                "reasoning": "Move stop loss to breakeven plus spread",
                                "risk_level": "LOW"
                            })
                        }
                    }]
                }
                mock_post.return_value.__aenter__.return_value = mock_response
                
                # Mock successful modification
                mock_mt5.order_send.return_value = Mock(retcode=10009)
                
                # Run trade management
                await trade_manager.manage_trades()
                
                # Verify position was managed
                mock_mt5.order_send.assert_called()
    
    def test_magic_number_tracking(self):
        """Test magic number assignment and tracking across strategies"""
        
        # Test that different strategies get different magic numbers
        magic_numbers = set()
        
        from strategies.base_strategy import StrategyType
        strategy_types = [StrategyType.TREND_FOLLOWING, StrategyType.MEAN_REVERSION, StrategyType.BREAKOUT]

        for strategy_type in strategy_types:
            strategy = self.account_manager.strategy_factory.create_strategy(strategy_type, {'magic_number': 12345})
            magic_numbers.add(strategy.magic_number)
        
        # All magic numbers should be unique
        self.assertEqual(len(magic_numbers), len(strategy_types))
        
        # Magic numbers should be in valid range
        for magic in magic_numbers:
            self.assertGreater(magic, 0)
            self.assertLess(magic, 999999)
    
    async def test_error_recovery_scenarios(self):
        """Test system behavior during error conditions"""
        
        with patch('mt5_integration.mt5_client.mt5') as mock_mt5:
            # Test MT5 connection failure
            mock_mt5.initialize.return_value = False
            
            signal_generator = SignalGenerator(self.account_manager)
            
            # Should handle gracefully without crashing
            try:
                await signal_generator.generate_signals()
                # Should not raise exception
            except Exception as e:
                self.fail(f"Signal generator should handle MT5 failure gracefully: {e}")
            
            # Test order placement failure
            mock_mt5.initialize.return_value = True
            mock_mt5.login.return_value = True
            mock_mt5.order_send.return_value = Mock(
                retcode=10013,  # TRADE_RETCODE_INVALID_REQUEST
                comment="Invalid request"
            )
            
            # Should log error and continue
            try:
                await signal_generator.generate_signals()
            except Exception as e:
                self.fail(f"Should handle order failure gracefully: {e}")

def run_integration_tests():
    """Run all integration tests"""
    unittest.main(verbosity=2)

if __name__ == "__main__":
    run_integration_tests()
