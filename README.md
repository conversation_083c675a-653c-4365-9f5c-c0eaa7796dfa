# 🤖 AI-Driven Trading System

A sophisticated algorithmic trading system that leverages artificial intelligence to generate trading signals and manage positions across MetaTrader 5 accounts. Built with advanced risk management, cost optimization, and real-time market analysis.

## 🎯 **Live Trading Ready**
- ✅ **Real Account Tested** - Successfully executed live trades
- ✅ **Risk Management** - Multiple layers of protection
- ✅ **Cost Optimized** - ~$14/month API costs
- ✅ **Profitable Configuration** - Optimized for small accounts

## 🚀 **Key Features**

### 🧠 **AI Integration**
- **Qwen AI API**: Advanced language model for market analysis
- **Dynamic Prompts**: 10,000+ character comprehensive market analysis
- **Multi-timeframe Analysis**: H1, H4, D1 timeframe support
- **Confidence Scoring**: AI provides 0-1 confidence levels
- **Real-time Decisions**: 15-minute signal generation cycle

### 💰 **Money Management Strategies**
1. **Fixed Volume**: Consistent lot size for all trades
2. **Percent Risk**: Risk fixed percentage of account balance per trade  
3. **Martingale**: Double position size after losses (with safety limits)
4. **Anti-Martingale**: Increase position size after wins

### 📈 **Trading Strategies**
1. **Trend Following**: Multi-timeframe trend analysis and momentum confirmation
2. **Mean Reversion**: Overbought/oversold conditions for ranging markets
3. **Breakout**: Pattern recognition with volume confirmation

### 🛡️ **Risk Management**
- **Daily Trade Limits**: Configurable maximum trades per day
- **Position Limits**: Maximum concurrent open positions
- **Daily Loss Limits**: Stop trading after daily loss threshold
- **Drawdown Protection**: Account-level drawdown monitoring
- **Stop Loss Enforcement**: Mandatory stop losses on all trades

## 📋 **Requirements**

### System Requirements
- Python 3.8+
- MetaTrader 5 terminal
- Windows OS (for MT5 integration)
- Qwen AI API access

### Dependencies
```bash
pip install -r requirements.txt
```

## 🚀 **Quick Start**

### 1. Clone Repository
```bash
git clone https://github.com/daadbina/fulltrade.git
cd fulltrade
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Environment Setup
Create `.env` file:
```env
# Qwen AI API Configuration
QWEN_API_KEY=your_qwen_api_key_here
QWEN_API_URL=https://dashscope-intl.aliyuncs.com/compatible-mode/v1
AI_MODEL=qwen-max-2025-01-25

# Trading Configuration (Optimized for Cost)
SIGNAL_GENERATION_INTERVAL=900  # 15 minutes
TRADE_MANAGEMENT_INTERVAL=300   # 5 minutes

# Risk Management (Optimized for $100 Account)
MAX_DAILY_TRADES_PER_ACCOUNT=5
MAX_OPEN_POSITIONS=3
MAX_DAILY_LOSS_DOLLARS=5
MAX_DRAWDOWN_PERCENT=10
```

### 4. Account Configuration
Create `config/accounts.json`:
```json
{
  "accounts": [
    {
      "account_id": "your_account_1",
      "account_number": ********,
      "password": "your_password",
      "server": "YourBroker-Server",
      "strategy": "trend_following",
      "money_management": "percent_risk",
      "symbols": [
        {"symbol": "EURUSD", "timeframe": "H4"},
        {"symbol": "GBPUSD", "timeframe": "H4"}
      ],
      "money_management_settings": {
        "risk_percent": 1.5,
        "max_risk_per_trade": 15.0,
        "stop_loss_pips": 30,
        "take_profit_ratio": 2.0
      }
    }
  ]
}
```

### 5. Run System
```bash
python trading_system_main.py
```

## 🧪 **Testing**

### Test MT5 Connection
```bash
python test_mt5_connections.py
```

### Test Real Account Execution
```bash
python test_real_account_execution.py
```

### Test Environment Variables
```bash
python test_env_variables.py
```

## 💰 **Cost Analysis**

### Current Optimized Settings:
- **Signal Generation**: Every 15 minutes
- **Daily API Calls**: ~96 calls
- **Monthly Cost**: ~$14.40
- **Cost per Trade**: ~$0.15

### Cost Optimization:
- Reduced from 5-minute to 15-minute intervals
- 78% cost reduction achieved
- Maintains trading effectiveness

## 🛡️ **Risk Management**

### Account Protection:
- **Max Daily Loss**: $5 (5% of $100 account)
- **Position Sizing**: 1.5% risk per trade
- **Stop Loss**: 30 pips maximum
- **Take Profit**: 2:1 risk/reward ratio
- **Daily Trade Limit**: 5 trades maximum

### System Safeguards:
- Market hours validation
- Overtrading prevention
- Emergency stop mechanisms
- Real-time monitoring

## 📊 **Performance Features**

### Real-time Monitoring:
- Live P&L tracking
- Position management
- Risk metrics
- Trade statistics

### Logging System:
- Comprehensive trade logs
- AI decision tracking
- Error monitoring
- Performance analytics

## ⚠️ **Important Notes**

### Security:
- Never commit `.env` or `accounts.json` files
- Use demo accounts for testing
- Keep API keys secure

### Risk Warning:
- Trading involves financial risk
- Test thoroughly before live trading
- Never risk more than you can afford to lose
- Monitor system performance regularly

## 📁 **Project Structure**

```
fulltrade/
├── src/
│   ├── account_management/     # Account management
│   ├── ai_integration/         # AI API integration
│   ├── logging_system/         # Logging system
│   ├── money_management/       # Money management strategies
│   ├── mt5_integration/        # MetaTrader 5 integration
│   ├── signal_generation/      # Signal generation
│   ├── strategies/             # Trading strategies
│   └── trade_management/       # Trade management
├── config/                     # Configuration files
├── logs/                       # Log files
├── tests/                      # Test scripts
└── docs/                       # Documentation
```

## 🤝 **Contributing**

1. Fork the repository
2. Create feature branch
3. Test thoroughly
4. Submit pull request

## 📄 **License**

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 **Support**

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review test scripts for examples

---

**⚠️ DISCLAIMER**: This software is for educational purposes. Trading involves risk of financial loss. Use at your own risk.**
