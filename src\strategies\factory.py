"""
Trading Strategy Factory
"""

from typing import Dict, Any
from strategies.base_strategy import BaseStrategy, StrategyType
from strategies.trend_following import TrendFollowingStrategy
from strategies.mean_reversion import MeanReversionStrategy
from strategies.breakout import BreakoutStrategy

class StrategyFactory:
    """Factory for creating trading strategy instances"""
    
    _strategies = {
        StrategyType.TREND_FOLLOWING: TrendFollowingStrategy,
        StrategyType.MEAN_REVERSION: MeanReversionStrategy,
        StrategyType.BREAKOUT: BreakoutStrategy,
    }
    
    @classmethod
    def create_strategy(
        cls, 
        strategy_type: StrategyType, 
        config: Dict[str, Any]
    ) -> BaseStrategy:
        """Create a trading strategy instance"""
        
        if strategy_type not in cls._strategies:
            raise ValueError(f"Unknown trading strategy: {strategy_type}")
        
        strategy_class = cls._strategies[strategy_type]
        return strategy_class(config)
    
    @classmethod
    def get_available_strategies(cls) -> list:
        """Get list of available strategy types"""
        return list(cls._strategies.keys())
    
    @classmethod
    def get_default_config(cls, strategy_type: StrategyType) -> Dict[str, Any]:
        """Get default configuration for a strategy type"""
        
        default_configs = {
            StrategyType.TREND_FOLLOWING: {
                'magic_number': 1001,
                'timeframes': ['H1', 'H4'],
                'symbols': ['EURUSD', 'GBPUSD', 'USDJPY'],
                'max_spread': 2.0,
                'min_volatility': 0.0001,
            },
            StrategyType.MEAN_REVERSION: {
                'magic_number': 2001,
                'timeframes': ['M15', 'M30'],
                'symbols': ['EURUSD', 'GBPUSD'],
                'max_spread': 1.5,
                'max_hold_hours': 4,
            },
            StrategyType.BREAKOUT: {
                'magic_number': 3001,
                'timeframes': ['H1', 'H4'],
                'symbols': ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD'],
                'max_spread': 2.5,
                'average_volume': 1000,
            },
        }
        
        return default_configs.get(strategy_type, {})
