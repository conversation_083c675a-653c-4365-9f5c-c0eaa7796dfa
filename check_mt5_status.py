#!/usr/bin/env python3
"""
Check MT5 status and try to fix connection issues
"""

import MetaTrader5 as mt5
import os
import time

def check_mt5_status():
    """Check MT5 status and try to fix issues"""
    print("🔍 CHECKING MT5 STATUS")
    print("=" * 50)
    
    # Check if MT5 is already initialized
    print("1. Checking if MT5 is already initialized...")
    try:
        terminal_info = mt5.terminal_info()
        if terminal_info is not None:
            print(f"✅ MT5 is initialized")
            print(f"   Connected: {terminal_info.connected}")
            print(f"   Build: {terminal_info.build}")
            print(f"   Company: {terminal_info.company}")
            print(f"   Path: {terminal_info.path}")
            
            # Check account info
            account_info = mt5.account_info()
            if account_info is not None:
                print(f"✅ Account logged in: {account_info.login}")
                print(f"   Balance: ${account_info.balance:.2f}")
                print(f"   Trading allowed: {account_info.trade_allowed}")
                return True
            else:
                print("⚠️ MT5 initialized but no account logged in")
        else:
            print("❌ MT5 not initialized")
    except Exception as e:
        print(f"❌ Error checking MT5 status: {e}")
    
    # Try to shutdown and reinitialize
    print("\n2. Attempting to shutdown MT5...")
    try:
        mt5.shutdown()
        print("✅ MT5 shutdown successful")
        time.sleep(2)
    except Exception as e:
        print(f"⚠️ Error during shutdown: {e}")
    
    # Try to initialize MT5
    print("\n3. Attempting to initialize MT5...")
    mt5_path = r'C:\Program Files\MetaTrader 5\terminal64.exe'
    
    try:
        if not os.path.exists(mt5_path):
            print(f"❌ MT5 executable not found at: {mt5_path}")
            return False
        
        print(f"✅ MT5 executable found at: {mt5_path}")
        
        # Try initialization
        if mt5.initialize(path=mt5_path):
            print("✅ MT5 initialization successful")
            
            # Check terminal info
            terminal_info = mt5.terminal_info()
            if terminal_info:
                print(f"✅ Terminal connected: {terminal_info.connected}")
                print(f"   Build: {terminal_info.build}")
                return True
            else:
                print("❌ Cannot get terminal info after initialization")
                return False
        else:
            error = mt5.last_error()
            print(f"❌ MT5 initialization failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during initialization: {e}")
        return False

def try_login():
    """Try to login to the demo account"""
    print("\n4. Attempting to login to demo account...")

    # Load credentials from config file
    import json
    try:
        with open('config/accounts.json', 'r') as f:
            config = json.load(f)
        account = config['accounts'][0]
        account_number = account['account_number']
        password = account['password']
        server = account['server']
        print(f"   Using account: {account_number}")
        print(f"   Server: {server}")
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False

    try:
        login_result = mt5.login(
            login=account_number,
            password=password,
            server=server
        )
        
        if login_result:
            print("✅ Login successful")
            
            # Verify account
            account_info = mt5.account_info()
            if account_info:
                print(f"✅ Account verified: {account_info.login}")
                print(f"   Balance: ${account_info.balance:.2f}")
                print(f"   Trading allowed: {account_info.trade_allowed}")
                return True
            else:
                print("❌ Cannot get account info after login")
                return False
        else:
            error = mt5.last_error()
            print(f"❌ Login failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during login: {e}")
        return False

def main():
    """Main function"""
    print("🚀 MT5 CONNECTION DIAGNOSTIC TOOL")
    print("=" * 60)
    
    # Check status
    status_ok = check_mt5_status()
    
    if not status_ok:
        print("\n❌ MT5 status check failed")
        return False
    
    # Try login
    login_ok = try_login()
    
    if login_ok:
        print("\n🎉 MT5 CONNECTION FULLY OPERATIONAL")
        print("✅ MT5 is initialized and account is logged in")
        print("✅ Ready for trading operations")
        
        # Test basic operations
        print("\n5. Testing basic operations...")
        
        # Get positions
        positions = mt5.positions_get()
        print(f"✅ Positions retrieved: {len(positions) if positions else 0}")
        
        # Get orders
        orders = mt5.orders_get()
        print(f"✅ Orders retrieved: {len(orders) if orders else 0}")
        
        return True
    else:
        print("\n❌ MT5 CONNECTION FAILED")
        print("❌ Cannot establish proper connection")
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n💡 TROUBLESHOOTING SUGGESTIONS:")
        print("1. Close MetaTrader 5 application completely")
        print("2. Restart MetaTrader 5 application")
        print("3. Ensure account credentials are correct")
        print("4. Check internet connection")
        print("5. Try running this script as administrator")
