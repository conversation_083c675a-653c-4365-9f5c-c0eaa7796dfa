#!/usr/bin/env python3
"""
Comprehensive Test Suite for AI-Driven Trading System
Pre-Production Testing for Real Account Deployment
"""

import unittest
import asyncio
import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
import time

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Import all components
from account_management.account_manager import AccountManager
from account_management.models import TradingAccount, AccountBalance
from money_management.percent_risk import PercentRiskStrategy
from money_management.fixed_volume import FixedVolumeStrategy
from money_management.martingale import MartingaleStrategy
from money_management.anti_martingale import AntiMartingaleStrategy
from money_management.base_strategy import AccountInfo, TradeParameters
from strategies.trend_following import TrendFollowingStrategy
from strategies.mean_reversion import MeanReversionStrategy
from strategies.breakout import BreakoutStrategy
from strategies.base_strategy import MarketData, TradingSignal, StrategyType
from ai_integration.qwen_client import QwenClient
from ai_integration.prompt_builder import PromptBuilder
from mt5_integration.mt5_client import MT5Client
from signal_generation.signal_generator import SignalGenerator
from trade_management.trade_manager import TradeManager
from logging_system.logger import setup_logger, trading_logger
from validation.trade_validator import TradeValidator

class TestAccountManagement(unittest.TestCase):
    """Comprehensive tests for account management"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.test_config_file = os.path.join(self.test_dir, "test_accounts.json")
        
        # Create comprehensive test configuration
        self.test_config = {
            "accounts": [
                {
                    "account_id": "test_account_1",
                    "account_number": ********,
                    "password": "test_password",
                    "server": "RoboForex-ECN",
                    "strategy": "trend_following",
                    "money_management": "percent_risk",
                    "symbols": [
                        {"symbol": "EURUSD", "timeframe": "H1"},
                        {"symbol": "GBPUSD", "timeframe": "H4"}
                    ],
                    "money_management_settings": {
                        "risk_percent": 2.0,
                        "max_daily_risk_percent": 10.0
                    },
                    "max_daily_trades": 5,
                    "max_concurrent_positions": 3,
                    "trading_enabled": True
                },
                {
                    "account_id": "test_account_2",
                    "account_number": ********,
                    "password": "test_password_2",
                    "server": "RoboForex-ECN",
                    "strategy": "mean_reversion",
                    "money_management": "fixed_volume",
                    "symbols": [
                        {"symbol": "USDJPY", "timeframe": "H1"}
                    ],
                    "money_management_settings": {
                        "fixed_volume": 0.01
                    },
                    "max_daily_trades": 10,
                    "max_concurrent_positions": 2,
                    "trading_enabled": True
                }
            ],
            "groups": [
                {
                    "group_id": "forex_major",
                    "accounts": ["test_account_1", "test_account_2"],
                    "strategy_type": "trend_following",
                    "money_management_type": "percent_risk"
                }
            ]
        }
        
        with open(self.test_config_file, 'w') as f:
            json.dump(self.test_config, f)
        
        self.account_manager = AccountManager(config_path=self.test_config_file)
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir)
    
    def test_load_accounts_comprehensive(self):
        """Test comprehensive account loading"""
        result = self.account_manager.load_accounts()
        self.assertTrue(result)
        self.assertEqual(len(self.account_manager.accounts), 2)
        self.assertEqual(len(self.account_manager.account_groups), 1)
        
        # Test account details
        account1 = self.account_manager.get_account("test_account_1")
        self.assertIsNotNone(account1)
        self.assertEqual(account1.account_number, ********)
        self.assertEqual(account1.strategy_type, "trend_following")
        self.assertEqual(len(account1.symbols), 2)
        
        # Test account group
        group = self.account_manager.account_groups["forex_major"]
        self.assertEqual(len(group.accounts), 2)
    
    def test_account_validation(self):
        """Test account configuration validation"""
        # Test invalid account configuration
        invalid_config = {
            "accounts": [
                {
                    "account_id": "invalid_account",
                    # Missing required fields
                }
            ]
        }
        
        invalid_file = os.path.join(self.test_dir, "invalid_accounts.json")
        with open(invalid_file, 'w') as f:
            json.dump(invalid_config, f)
        
        invalid_manager = AccountManager(config_path=invalid_file)
        # Should handle gracefully without crashing
        result = invalid_manager.load_accounts()
        # Depending on implementation, this might return False or handle gracefully
    
    def test_strategy_factory_all_types(self):
        """Test strategy factory for all strategy types"""
        from strategies.base_strategy import StrategyType
        strategy_types = [StrategyType.TREND_FOLLOWING, StrategyType.MEAN_REVERSION, StrategyType.BREAKOUT]

        for strategy_type in strategy_types:
            strategy = self.account_manager.strategy_factory.create_strategy(strategy_type, {'magic_number': 12345})
            self.assertIsNotNone(strategy)
            self.assertEqual(strategy.get_strategy_type(), strategy_type)
    
    def test_money_management_factory_all_types(self):
        """Test money management factory for all types"""
        from money_management.base_strategy import MoneyManagementType
        mm_types = [MoneyManagementType.FIXED_VOLUME, MoneyManagementType.PERCENT_RISK, MoneyManagementType.MARTINGALE, MoneyManagementType.ANTI_MARTINGALE]

        for mm_type in mm_types:
            mm_strategy = self.account_manager.money_management_factory.create_strategy(mm_type, {'risk_percent': 2.0})
            self.assertIsNotNone(mm_strategy)
            self.assertEqual(mm_strategy.get_strategy_type(), mm_type)

class TestMoneyManagementStrategies(unittest.TestCase):
    """Comprehensive tests for all money management strategies"""
    
    def setUp(self):
        """Set up test data"""
        self.account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        self.market_data = {
            'pip_value': 1.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }
        
        self.trade_history = [
            {'profit': 100.0, 'symbol': 'EURUSD', 'close_time': datetime.now()},
            {'profit': -50.0, 'symbol': 'EURUSD', 'close_time': datetime.now()},
            {'profit': 75.0, 'symbol': 'EURUSD', 'close_time': datetime.now()},
        ]
    
    def test_percent_risk_strategy_comprehensive(self):
        """Test percent risk strategy with various scenarios"""
        config = {'risk_percent': 2.0, 'default_volume': 0.01}
        strategy = PercentRiskStrategy(config)
        
        # Test with stop loss
        trade_params = strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=self.trade_history,
            market_data=self.market_data
        )
        
        self.assertIsInstance(trade_params, TradeParameters)
        self.assertGreater(trade_params.volume, 0)
        self.assertEqual(trade_params.risk_amount, 200.0)  # 2% of 10000
        
        # Test without stop loss
        trade_params_no_sl = strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=None,
            trade_history=self.trade_history,
            market_data=self.market_data
        )
        
        self.assertEqual(trade_params_no_sl.volume, 0.01)
        self.assertEqual(trade_params_no_sl.confidence_level, 0.3)
    
    def test_fixed_volume_strategy(self):
        """Test fixed volume strategy"""
        config = {'fixed_volume': 0.05}
        strategy = FixedVolumeStrategy(config)
        
        trade_params = strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=self.trade_history,
            market_data=self.market_data
        )
        
        self.assertEqual(trade_params.volume, 0.05)
        self.assertGreater(trade_params.risk_amount, 0)
    
    def test_martingale_strategy(self):
        """Test martingale strategy with consecutive losses"""
        config = {'base_volume': 0.01, 'max_multiplier': 8}
        strategy = MartingaleStrategy(config)
        
        # Test with no losses (should use base volume)
        winning_history = [{'profit': 100.0, 'symbol': 'EURUSD'}]
        trade_params = strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=winning_history,
            market_data=self.market_data
        )
        
        self.assertEqual(trade_params.volume, 0.01)
        
        # Test with consecutive losses
        losing_history = [
            {'profit': -50.0, 'symbol': 'EURUSD'},
            {'profit': -50.0, 'symbol': 'EURUSD'}
        ]
        trade_params_loss = strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=losing_history,
            market_data=self.market_data
        )
        
        # Should increase volume after losses
        self.assertGreater(trade_params_loss.volume, 0.01)
    
    def test_anti_martingale_strategy(self):
        """Test anti-martingale strategy with consecutive wins"""
        config = {'base_volume': 0.01, 'max_multiplier': 4}
        strategy = AntiMartingaleStrategy(config)
        
        # Test with consecutive wins
        winning_history = [
            {'profit': 100.0, 'symbol': 'EURUSD'},
            {'profit': 75.0, 'symbol': 'EURUSD'}
        ]
        trade_params = strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=winning_history,
            market_data=self.market_data
        )
        
        # Should increase volume after wins
        self.assertGreater(trade_params.volume, 0.01)
        self.assertLessEqual(trade_params.volume, 0.04)  # Max 4x base
    
    def test_ai_prompts_all_strategies(self):
        """Test AI prompt generation for all money management strategies"""
        strategies = [
            PercentRiskStrategy({'risk_percent': 2.0}),
            FixedVolumeStrategy({'fixed_volume': 0.01}),
            MartingaleStrategy({'base_volume': 0.01}),
            AntiMartingaleStrategy({'base_volume': 0.01})
        ]
        
        for strategy in strategies:
            prompt = strategy.get_ai_prompt(self.account_info, self.trade_history)
            self.assertIsInstance(prompt, str)
            self.assertGreater(len(prompt), 100)  # Should be substantial
            self.assertIn("money management", prompt.lower())

class TestTradingStrategies(unittest.TestCase):
    """Comprehensive tests for all trading strategies"""

    def setUp(self):
        """Set up test data"""
        self.market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[
                {'time': datetime.now(), 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000},
                {'time': datetime.now(), 'open': 1.1010, 'high': 1.1030, 'low': 1.0990, 'close': 1.1020, 'volume': 1200},
                {'time': datetime.now(), 'open': 1.1020, 'high': 1.1040, 'low': 1.1000, 'close': 1.1030, 'volume': 1100},
            ],
            current_price=1.1030,
            spread=1.5,
            volume=1100,
            volatility=0.0015
        )

        self.trade_history = [
            {'profit': 100.0, 'magic_number': 12345, 'symbol': 'EURUSD'},
            {'profit': -50.0, 'magic_number': 12345, 'symbol': 'EURUSD'},
        ]

        self.account_info = {
            'balance': 10000.0,
            'equity': 10000.0,
            'currency': 'USD'
        }

    def test_trend_following_strategy(self):
        """Test trend following strategy"""
        config = {'magic_number': 12345}
        strategy = TrendFollowingStrategy(config)

        self.assertEqual(strategy.get_strategy_type(), StrategyType.TREND_FOLLOWING)
        self.assertEqual(strategy.get_strategy_name(), "Advanced Trend Following")

        # Test AI prompt generation
        prompt = strategy.get_ai_prompt(self.market_data, self.trade_history, self.account_info)
        self.assertIsInstance(prompt, str)
        self.assertIn("TREND FOLLOWING", prompt)
        self.assertIn("EURUSD", prompt)
        self.assertIn("trend direction", prompt.lower())

        # Test signal validation
        valid_signal = TradingSignal(
            action="BUY",
            confidence=0.8,
            entry_price=1.1030,
            stop_loss=1.1000,
            take_profit=1.1080,
            reasoning="Strong uptrend confirmed",
            risk_level="MEDIUM"
        )

        self.assertTrue(strategy.validate_signal(valid_signal, self.market_data))

    def test_mean_reversion_strategy(self):
        """Test mean reversion strategy"""
        config = {'magic_number': 12346}
        strategy = MeanReversionStrategy(config)

        self.assertEqual(strategy.get_strategy_type(), StrategyType.MEAN_REVERSION)

        prompt = strategy.get_ai_prompt(self.market_data, self.trade_history, self.account_info)
        self.assertIn("MEAN REVERSION", prompt)
        self.assertIn("overbought", prompt.lower())
        self.assertIn("oversold", prompt.lower())

    def test_breakout_strategy(self):
        """Test breakout strategy"""
        config = {'magic_number': 12347}
        strategy = BreakoutStrategy(config)

        self.assertEqual(strategy.get_strategy_type(), StrategyType.BREAKOUT)

        prompt = strategy.get_ai_prompt(self.market_data, self.trade_history, self.account_info)
        self.assertIn("BREAKOUT", prompt)
        self.assertIn("consolidation", prompt.lower())
        self.assertIn("volume", prompt.lower())

class TestAIIntegration(unittest.TestCase):
    """Comprehensive tests for AI integration"""

    def setUp(self):
        """Set up test environment"""
        self.prompt_builder = PromptBuilder()

        # Mock strategy
        self.strategy = Mock()
        self.strategy.get_ai_prompt.return_value = "Test strategy prompt with detailed analysis"
        self.strategy.magic_number = 12345

        # Mock money management
        self.money_management = Mock()
        self.money_management.get_ai_prompt.return_value = "Test money management prompt with risk calculations"

        # Test market data
        self.market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[
                {'time': datetime.now(), 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000}
            ],
            current_price=1.1010,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )

        self.account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )

        self.trade_history = []

    def test_prompt_builder_comprehensive(self):
        """Test comprehensive prompt building"""
        prompt = self.prompt_builder.build_trading_prompt(
            strategy=self.strategy,
            money_management=self.money_management,
            market_data=self.market_data,
            trade_history=self.trade_history,
            account_info=self.account_info,
            additional_context={'test_context': 'value'}
        )

        self.assertIsInstance(prompt, str)
        self.assertGreater(len(prompt), 500)  # Should be comprehensive

        # Check that all components are included
        self.assertIn("Test strategy prompt", prompt)
        self.assertIn("Test money management prompt", prompt)
        self.assertIn("EURUSD", prompt)
        self.assertIn("CURRENT MARKET CONDITIONS", prompt)
        self.assertIn("INTEGRATION INSTRUCTIONS", prompt)
        self.assertIn("RESPONSE REQUIREMENTS", prompt)
        self.assertIn("JSON format", prompt)

    def test_prompt_builder_error_handling(self):
        """Test prompt builder error handling"""
        # Test with failing strategy
        failing_strategy = Mock()
        failing_strategy.get_ai_prompt.side_effect = Exception("Strategy error")

        # Should not crash and should provide fallback
        prompt = self.prompt_builder.build_trading_prompt(
            strategy=failing_strategy,
            money_management=self.money_management,
            market_data=self.market_data,
            trade_history=self.trade_history,
            account_info=self.account_info
        )

        self.assertIsInstance(prompt, str)
        # Should contain fallback content
        self.assertIn("EMERGENCY", prompt)

    @patch.dict(os.environ, {'QWEN_API_KEY': 'test_key'})
    def test_qwen_client_initialization(self):
        """Test Qwen client initialization"""
        client = QwenClient()
        self.assertEqual(client.api_key, 'test_key')
        self.assertIn('chat/completions', client.api_url)

    @patch.dict(os.environ, {'QWEN_API_KEY': 'test_key'})
    @patch('aiohttp.ClientSession.post')
    async def test_qwen_client_api_call(self, mock_post):
        """Test Qwen client API call"""
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            'choices': [{
                'message': {
                    'content': '{"action": "BUY", "confidence": 0.8, "reasoning": "Test response"}'
                }
            }]
        }
        mock_post.return_value.__aenter__.return_value = mock_response

        client = QwenClient()
        response = await client.generate_trading_decision("Test prompt")

        self.assertIsInstance(response, dict)
        self.assertIn('action', response)
        self.assertEqual(response['action'], 'BUY')

    @patch.dict(os.environ, {'QWEN_API_KEY': 'test_key'})
    @patch('aiohttp.ClientSession.post')
    async def test_qwen_client_error_handling(self, mock_post):
        """Test Qwen client error handling"""
        # Mock error response
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = "Internal server error"
        mock_post.return_value.__aenter__.return_value = mock_response

        client = QwenClient()
        response = await client.generate_trading_decision("Test prompt")

        self.assertIsInstance(response, dict)
        self.assertEqual(response['action'], 'HOLD')
        self.assertIn('error', response['reasoning'].lower())

class TestMT5Integration(unittest.TestCase):
    """Comprehensive tests for MT5 integration"""

    def setUp(self):
        """Set up test environment"""
        self.mt5_client = MT5Client()

        # Mock account
        self.test_account = TradingAccount(
            account_id="test_account",
            account_number=********,
            password="test_password",
            server="RoboForex-ECN",
            username="test_user",
            strategy_type="trend_following",
            money_management_type="percent_risk",
            symbols=[{"symbol": "EURUSD", "timeframe": "H1"}],
            timeframes=["H1"],
            money_management_config={"risk_percent": 2.0}
        )

    @patch('MetaTrader5.initialize')
    @patch('MetaTrader5.login')
    @patch('MetaTrader5.account_info')
    def test_mt5_login_comprehensive(self, mock_account_info, mock_login, mock_initialize):
        """Test comprehensive MT5 login functionality"""
        mock_initialize.return_value = True
        mock_login.return_value = True
        mock_account_info.return_value = Mock(
            login=********,
            server="RoboForex-ECN",
            balance=10000.0,
            equity=10000.0
        )

        result = self.mt5_client.login(self.test_account)
        self.assertTrue(result)
        self.assertEqual(self.mt5_client.current_account, self.test_account)

    @patch('MetaTrader5.copy_rates_from_pos')
    @patch('MetaTrader5.symbol_info_tick')
    @patch('MetaTrader5.symbol_info')
    def test_market_data_retrieval(self, mock_symbol_info, mock_tick, mock_rates):
        """Test market data retrieval"""
        # Mock market data
        mock_rates.return_value = [
            (datetime.now().timestamp(), 1.1000, 1.1020, 1.0980, 1.1010, 0, 1000, 0),
            (datetime.now().timestamp(), 1.1010, 1.1030, 1.0990, 1.1020, 0, 1200, 0),
        ]

        mock_tick.return_value = Mock(bid=1.1018, ask=1.1020)
        mock_symbol_info.return_value = Mock(
            volume_min=0.01,
            volume_max=100.0,
            point=0.00001
        )

        market_data = self.mt5_client.get_market_data("EURUSD", "H1", 200)

        self.assertIsNotNone(market_data)
        self.assertEqual(market_data['symbol'], "EURUSD")
        self.assertEqual(market_data['timeframe'], "H1")
        self.assertIn('candles', market_data)
        self.assertIn('current_price', market_data)
        self.assertIn('volatility', market_data)

    def test_market_data_error_handling(self):
        """Test market data error handling"""
        # Test with invalid timeframe
        market_data = self.mt5_client.get_market_data("EURUSD", "INVALID", 200)
        self.assertIsNone(market_data)
