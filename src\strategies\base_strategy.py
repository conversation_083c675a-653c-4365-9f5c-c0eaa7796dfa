"""
Base Trading Strategy Class
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

class StrategyType(Enum):
    TREND_FOLLOWING = "trend_following"
    MEAN_REVERSION = "mean_reversion"
    BREAKOUT = "breakout"
    SCALPING = "scalping"
    SWING_TRADING = "swing_trading"
    NEWS_TRADING = "news_trading"

@dataclass
class MarketData:
    """Market data structure for strategy analysis"""
    symbol: str
    timeframe: str
    candles: List[Dict[str, Any]]  # OHLCV data
    current_price: float
    spread: float
    volume: float
    volatility: float

@dataclass
class TradingSignal:
    """Trading signal generated by strategy"""
    action: str  # "BUY", "SELL", "HOLD", "CLOSE"
    confidence: float  # 0.0 to 1.0
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    reasoning: str
    risk_level: str  # "LOW", "MEDIUM", "HIGH"

class BaseStrategy(ABC):
    """Base class for all trading strategies"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.strategy_type = self.get_strategy_type()
        self.magic_number = config.get('magic_number', 0)
    
    @abstractmethod
    def get_strategy_type(self) -> StrategyType:
        """Return the strategy type"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """Return the strategy name"""
        pass
    
    @abstractmethod
    def get_ai_prompt(
        self, 
        market_data: MarketData,
        trade_history: List[Dict[str, Any]],
        account_info: Dict[str, Any]
    ) -> str:
        """Get AI prompt for this strategy"""
        pass
    
    @abstractmethod
    def validate_signal(self, signal: TradingSignal, market_data: MarketData) -> bool:
        """Validate trading signal before execution"""
        pass
    
    def get_timeframes(self) -> List[str]:
        """Get supported timeframes for this strategy"""
        return self.config.get('timeframes', ['H1'])
    
    def get_symbols(self) -> List[str]:
        """Get supported symbols for this strategy"""
        return self.config.get('symbols', ['EURUSD'])
    
    def is_market_suitable(self, market_data: MarketData) -> bool:
        """Check if current market conditions are suitable for this strategy"""
        # Basic checks - can be overridden by specific strategies
        if market_data.spread > self.config.get('max_spread', 3.0):
            return False
        
        if market_data.volatility < self.config.get('min_volatility', 0.0001):
            return False
        
        return True
