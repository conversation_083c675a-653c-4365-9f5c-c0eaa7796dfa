#!/usr/bin/env python3
"""
Basic System Test for AI-Driven Trading System
"""

import sys
import os
import json
from pathlib import Path
from dotenv import load_dotenv

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing module imports...")

    try:
        # Test if all required files exist
        required_files = [
            "src/logging_system/logger.py",
            "src/account_management/account_manager.py",
            "src/money_management/fixed_volume.py",
            "src/money_management/percent_risk.py",
            "src/money_management/martingale.py",
            "src/money_management/anti_martingale.py",
            "src/strategies/trend_following.py",
            "src/strategies/mean_reversion.py",
            "src/strategies/breakout.py",
            "src/ai_integration/qwen_client.py",
            "src/ai_integration/prompt_builder.py",
            "src/mt5_integration/mt5_client.py",
            "src/signal_generation/signal_generator.py",
            "src/trade_management/trade_manager.py"
        ]

        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if missing_files:
            print(f"✗ Missing files: {missing_files}")
            return False

        print("✓ All required module files exist")

        # Test basic import that we know works
        from logging_system.logger import setup_logger, get_logger
        print("✓ Logging system imported successfully")

        return True

    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    # Load environment variables
    load_dotenv()
    
    # Check for required environment variables
    required_vars = [
        'QWEN_API_KEY',
        'MT5_PATH'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"✗ Missing environment variables: {missing_vars}")
        print("Please create a .env file with the required variables")
        return False
    else:
        print("✓ Environment variables configured")
        return True

def test_account_manager():
    """Test account manager initialization"""
    print("\nTesting account manager...")

    try:
        # Check if config file exists
        config_file = "config/accounts.json"
        if os.path.exists(config_file):
            print("✓ Account configuration file exists")

            # Try to load and validate JSON
            with open(config_file, 'r') as f:
                config_data = json.load(f)

            if 'accounts' in config_data and len(config_data['accounts']) > 0:
                print(f"✓ Configuration contains {len(config_data['accounts'])} accounts")

                # Validate first account structure
                first_account = config_data['accounts'][0]
                required_fields = ['account_id', 'account_number', 'password', 'server', 'strategy', 'money_management']

                missing_fields = [field for field in required_fields if field not in first_account]
                if not missing_fields:
                    print("✓ Account configuration structure is valid")
                else:
                    print(f"✗ Missing required fields: {missing_fields}")
                    return False
            else:
                print("✗ No accounts found in configuration")
                return False
        else:
            print("✗ Account configuration file not found")
            return False

        return True

    except Exception as e:
        print(f"✗ Account manager test failed: {e}")
        return False

def test_logging():
    """Test logging system"""
    print("\nTesting logging system...")
    
    try:
        from logging_system.logger import setup_logger, get_logger, trading_logger
        
        # Setup logger
        logger = setup_logger()
        print("✓ Logger setup successful")
        
        # Test basic logging
        test_logger = get_logger("test")
        test_logger.info("Test log message")
        print("✓ Basic logging working")
        
        # Test trading logger
        trading_logger.log_system_event("TEST", "System test event")
        print("✓ Trading logger working")
        
        return True
        
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False

def test_ai_integration():
    """Test AI integration components"""
    print("\nTesting AI integration...")

    try:
        # Check if required files exist
        ai_files = [
            "src/ai_integration/qwen_client.py",
            "src/ai_integration/prompt_builder.py"
        ]

        for file_path in ai_files:
            if os.path.exists(file_path):
                print(f"✓ {file_path} exists")
            else:
                print(f"✗ {file_path} not found")
                return False

        print("✓ AI integration files are present")

        return True

    except Exception as e:
        print(f"✗ AI integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("AI-Driven Trading System - Basic Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_configuration,
        test_logging,
        test_account_manager,
        test_ai_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! System is ready for use.")
        print("\nNext steps:")
        print("1. Configure your accounts in config/accounts.json")
        print("2. Test with demo accounts first")
        print("3. Run: python main.py")
    else:
        print("✗ Some tests failed. Please fix the issues before running the system.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
