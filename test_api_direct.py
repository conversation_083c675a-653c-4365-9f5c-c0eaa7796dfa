#!/usr/bin/env python3
"""
Direct AI API Test - Tests Qwen API without relative imports
"""

import asyncio
import os
import aiohttp
import json
from dotenv import load_dotenv

async def test_qwen_api_direct():
    """Test Qwen API directly"""
    print("🔍 Testing Qwen AI API Connection (Direct)...")
    
    # Load environment variables
    load_dotenv()
    
    # Get API configuration
    api_key = os.getenv('QWEN_API_KEY')
    api_url = os.getenv('QWEN_API_URL', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation')
    model = os.getenv('QWEN_MODEL', 'qwen-turbo')
    
    if not api_key:
        print("❌ QWEN_API_KEY not found")
        return False
    
    print(f"✓ API Key: {api_key[:10]}...{api_key[-4:]}")
    print(f"✓ API URL: {api_url}")
    print(f"✓ Model: {model}")
    
    # Prepare test request
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        "model": model,
        "input": {
            "messages": [
                {
                    "role": "user",
                    "content": "Please respond with a simple JSON: {\"status\": \"connected\", \"message\": \"API test successful\"}"
                }
            ]
        },
        "parameters": {
            "max_tokens": 100,
            "temperature": 0.1
        }
    }
    
    try:
        print("📡 Sending test request...")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, headers=headers, json=payload) as response:
                print(f"📊 Response Status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ API Response received:")
                    
                    # Extract the response text
                    if 'output' in result and 'text' in result['output']:
                        response_text = result['output']['text']
                        print(f"Response: {response_text}")
                        return True
                    else:
                        print(f"Unexpected response format: {result}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ API Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

async def main():
    """Main test function"""
    print("🤖 Direct AI API Connection Test")
    print("=" * 50)
    
    success = await test_qwen_api_direct()
    
    if success:
        print("\n🎉 AI API connection successful!")
        print("✅ Your Qwen API key is working correctly")
        print("✅ The trading system can make AI-driven decisions")
        print("\n📋 System is ready for trading!")
    else:
        print("\n❌ AI API connection failed")
        print("Please check:")
        print("- Your API key is correct")
        print("- You have internet connection")
        print("- Your Qwen account has sufficient credits")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
