#!/usr/bin/env python3
"""
Test script to verify pip value calculations
"""

import sys
sys.path.append('src')
from mt5_integration.mt5_client import MT5Client

def test_pip_calculations():
    """Test the corrected pip value calculation"""
    client = MT5Client()
    
    # Test symbols
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    
    print("=== PIP VALUE CALCULATIONS ===")
    for symbol in symbols:
        pip_size = client._get_pip_size(symbol)
        pip_value = client._get_pip_value(symbol)
        print(f'{symbol}:')
        print(f'  pip_size: {pip_size}')
        print(f'  pip_value: ${pip_value} per lot')
        print()
    
    print("=== RISK CALCULATION EXAMPLE ===")
    print('Account balance: $75')
    print('Risk percent: 0.5%')
    print('Risk amount: $0.375')
    print()
    
    # EURUSD example
    entry_price = 1.0850
    stop_loss = 1.0820
    pip_difference = abs(entry_price - stop_loss) / 0.0001
    pip_value = 10.0  # For EURUSD
    
    print(f'EURUSD: Entry {entry_price}, SL {stop_loss}')
    print(f'Pip difference: {pip_difference} pips')
    print(f'Required volume for $0.375 risk: {0.375 / (pip_difference * pip_value):.4f} lots')
    print(f'Actual risk with 0.01 lot: ${pip_difference * pip_value * 0.01:.2f}')
    print()
    
    # Show the problem
    print("=== THE PROBLEM ===")
    print("With 0.01 lot and 30 pip stop loss:")
    print(f"Risk = {pip_difference} pips × ${pip_value} per lot × 0.01 lot = ${pip_difference * pip_value * 0.01:.2f}")
    print("This is why we're seeing $3+ risks instead of $0.375!")
    print()

    # Test the rounding issue
    print("=== VOLUME ROUNDING ISSUE ===")
    calculated_volume = 0.375 / (pip_difference * pip_value)
    min_volume = 0.01

    print(f"Calculated volume: {calculated_volume:.6f} lots")
    print(f"Min volume: {min_volume}")

    # Current rounding logic (WRONG)
    wrong_volume = round(calculated_volume / min_volume) * min_volume
    print(f"Current rounding: round({calculated_volume:.6f} / {min_volume}) * {min_volume} = {wrong_volume}")
    print(f"Risk with wrong volume: ${pip_difference * pip_value * wrong_volume:.2f}")

    # Correct rounding logic (should round DOWN to stay within risk)
    import math
    correct_volume = math.floor(calculated_volume / min_volume) * min_volume
    print(f"Correct rounding: floor({calculated_volume:.6f} / {min_volume}) * {min_volume} = {correct_volume}")
    print(f"Risk with correct volume: ${pip_difference * pip_value * correct_volume:.2f}")

    # But if correct volume is 0, use min_volume
    if correct_volume == 0:
        print(f"Since correct volume is 0, must use min_volume {min_volume}")
        print(f"Risk with min_volume: ${pip_difference * pip_value * min_volume:.2f}")
        print("This exceeds the desired risk - need to adjust risk management!")

    print()
    print("=== TESTING SCENARIOS THAT LEAD TO 0.02, 0.03, 0.04 VOLUMES ===")

    # Test scenarios with different risk amounts that might round to 0.02, 0.03, 0.04
    test_scenarios = [
        ("Higher risk 1%", 75 * 0.01),  # $0.75 risk
        ("Higher risk 2%", 75 * 0.02),  # $1.50 risk
        ("Higher risk 3%", 75 * 0.03),  # $2.25 risk
        ("Different SL 20 pips", 0.375, 20),
        ("Different SL 15 pips", 0.375, 15),
        ("Different SL 10 pips", 0.375, 10),
    ]

    for scenario_name, risk_amount, *args in test_scenarios:
        pips = args[0] if args else 30
        calculated_vol = risk_amount / (pips * pip_value)
        rounded_vol = round(calculated_vol / min_volume) * min_volume
        actual_risk = pips * pip_value * rounded_vol

        print(f"{scenario_name}:")
        print(f"  Risk amount: ${risk_amount:.2f}")
        print(f"  Stop loss: {pips} pips")
        print(f"  Calculated volume: {calculated_vol:.6f}")
        print(f"  Rounded volume: {rounded_vol:.2f}")
        print(f"  Actual risk: ${actual_risk:.2f}")
        print()

if __name__ == "__main__":
    test_pip_calculations()
