"""
Anti-Martingale Money Management Strategy
"""

from typing import Dict, Any, Optional
from money_management.base_strategy import BaseMoneyManagement, MoneyManagementType, TradeParameters, AccountInfo

class AntiMartingaleStrategy(BaseMoneyManagement):
    """
    Anti-Martingale Money Management Strategy
    Increases position size after wins, decreases after losses
    """
    
    def get_strategy_type(self) -> MoneyManagementType:
        return MoneyManagementType.ANTI_MARTINGALE
    
    def calculate_position_size(
        self, 
        account_info: AccountInfo,
        symbol: str,
        entry_price: float,
        stop_loss: Optional[float],
        trade_history: list,
        market_data: Dict[str, Any]
    ) -> TradeParameters:
        """Calculate position size using anti-martingale progression"""
        
        base_volume = self.config.get('base_volume', 0.01)
        max_multiplier = self.config.get('max_multiplier', 4)  # Max 4x base volume
        
        # Count consecutive wins
        consecutive_wins = self._count_consecutive_wins(trade_history, symbol)
        
        # Calculate multiplier (increases with wins, resets on loss)
        if consecutive_wins == 0:
            multiplier = 1.0  # Base volume after loss or first trade
        else:
            # Gradual increase: 1.0, 1.5, 2.0, 2.5, 3.0, etc.
            multiplier = min(1.0 + (consecutive_wins * 0.5), max_multiplier)
        
        volume = base_volume * multiplier
        
        # Calculate risk-based volume limit to prevent excessive risk
        if stop_loss and entry_price:
            pip_value = market_data.get('pip_value', 10.0)
            pip_size = market_data.get('pip_size', 0.0001)
            pip_difference = abs(entry_price - stop_loss) / pip_size

            # Limit risk to maximum 10% of balance for anti-martingale
            max_risk_amount = account_info.balance * 0.10
            max_volume_by_risk = max_risk_amount / (pip_difference * pip_value) if pip_difference > 0 else volume

            # Apply risk-based limit
            volume = min(volume, max_volume_by_risk)

        # Ensure volume doesn't exceed account limits
        max_volume_by_margin = account_info.free_margin / 1000
        max_volume_by_balance = account_info.balance * 0.05 / 1000  # Max 5% of balance
        max_allowed_volume = min(max_volume_by_margin, max_volume_by_balance)

        volume = min(volume, max_allowed_volume)
        volume = max(volume, market_data.get('min_volume', 0.01))
        
        # Calculate risk amount
        risk_amount = 0
        if stop_loss:
            pip_value = market_data.get('pip_value', 1.0)
            pip_difference = abs(entry_price - stop_loss) / market_data.get('pip_size', 0.0001)
            risk_amount = volume * pip_difference * pip_value
        
        # Calculate confidence based on consecutive wins (higher after wins)
        confidence_level = min(0.9, 0.5 + (consecutive_wins * 0.1))
        
        return TradeParameters(
            volume=volume,
            stop_loss=stop_loss,
            take_profit=None,
            risk_amount=risk_amount,
            max_loss=risk_amount,
            confidence_level=confidence_level
        )

    def get_ai_prompt(self, account_info: AccountInfo, trade_history: list) -> str:
        """Get AI prompt for anti-martingale strategy"""

        recent_trades = trade_history[-20:] if len(trade_history) > 20 else trade_history
        consecutive_wins = self._count_consecutive_wins(trade_history, "current_symbol")
        win_rate = self._calculate_win_rate(recent_trades)
        profit_factor = self._calculate_profit_factor(recent_trades)

        base_volume = self.config.get('base_volume', 0.01)
        current_multiplier = min(1.0 + (consecutive_wins * 0.5), self.config.get('max_multiplier', 4))
        current_volume = base_volume * current_multiplier

        prompt = f"""
You are managing a trading account using an ANTI-MARTINGALE money management strategy.

ACCOUNT INFORMATION:
- Balance: ${account_info.balance:,.2f}
- Equity: ${account_info.equity:,.2f}
- Free Margin: ${account_info.free_margin:,.2f}
- Currency: {account_info.currency}
- Leverage: 1:{account_info.leverage}

MONEY MANAGEMENT STRATEGY: Anti-Martingale
- Base volume: {base_volume} lots
- Current consecutive wins: {consecutive_wins}
- Current multiplier: {current_multiplier:.1f}x
- Next trade volume: {current_volume:.2f} lots
- Maximum multiplier: {self.config.get('max_multiplier', 4)}x

RECENT PERFORMANCE:
- Win Rate: {win_rate:.1f}%
- Consecutive Wins: {consecutive_wins}
- Profit Factor: {profit_factor:.2f}
- Total Recent Trades: {len(recent_trades)}

ANTI-MARTINGALE STRATEGY GUIDELINES:
1. Increase position size after winning trades (ride the winning streak)
2. Reset to base volume after any losing trade (preserve capital)
3. Capitalize on trending markets and momentum
4. Risk increases gradually with success, not exponentially
5. Natural risk management through position size reduction after losses

STRATEGY ADVANTAGES:
- Limits losses during losing streaks (position size resets to base)
- Maximizes profits during winning streaks (position size increases)
- Psychologically easier to manage than martingale
- Better suited for trending markets and momentum strategies

CURRENT SITUATION:
{"🚀 WINNING STREAK: Increasing position size to capitalize on momentum!" if consecutive_wins >= 2 else "📊 NORMAL OPERATIONS: Standard position sizing in effect." if consecutive_wins == 1 else "🔄 RESET MODE: Back to base volume after recent loss."}

STRATEGY RULES:
1. Let winners run with increased position sizes
2. Cut losses quickly and reset position size
3. Focus on trend-following and momentum strategies
4. Maintain strict stop losses even with larger positions
5. Take partial profits to lock in gains during winning streaks

RISK MANAGEMENT:
- Maximum position size limited to {self.config.get('max_multiplier', 4)}x base volume
- Position size automatically resets after any loss
- Current risk level: {"ELEVATED" if consecutive_wins >= 3 else "MODERATE" if consecutive_wins >= 1 else "BASE"}

Based on the market data and current winning streak status, provide your trading decisions with:
1. Trend-following and momentum-based entry signals
2. Appropriate stop loss levels for current position size
3. Profit-taking strategies for winning streaks
4. Market trend analysis and momentum assessment
5. Position management for current {current_volume:.2f} lot size

Remember: Anti-martingale works best in trending markets. Focus on momentum and trend continuation.
Current position size is {current_volume:.2f} lots - use this size to maximize trending opportunities.
"""
        return prompt

    def _count_consecutive_wins(self, trade_history: list, symbol: str) -> int:
        """Count consecutive wins for the given symbol"""
        consecutive_wins = 0

        # Look at trades in reverse order (most recent first)
        for trade in reversed(trade_history):
            if trade.get('symbol') == symbol or symbol == "current_symbol":
                if trade.get('profit', 0) > 0:
                    consecutive_wins += 1
                else:
                    break  # Stop at first losing trade

        return consecutive_wins

    def _calculate_win_rate(self, trades: list) -> float:
        """Calculate win rate from trade history"""
        if not trades:
            return 0.0

        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        return (winning_trades / len(trades)) * 100

    def _calculate_profit_factor(self, trades: list) -> float:
        """Calculate profit factor (gross profit / gross loss)"""
        if not trades:
            return 0.0

        gross_profit = sum(trade.get('profit', 0) for trade in trades if trade.get('profit', 0) > 0)
        gross_loss = abs(sum(trade.get('profit', 0) for trade in trades if trade.get('profit', 0) < 0))

        return gross_profit / gross_loss if gross_loss > 0 else float('inf')
