"""
Martingale Money Management Strategy
"""

from typing import Dict, Any, Optional
from money_management.base_strategy import BaseMoneyManagement, MoneyManagementType, TradeParameters, AccountInfo

class MartingaleStrategy(BaseMoneyManagement):
    """
    Martingale Money Management Strategy
    Doubles position size after each loss to recover previous losses
    """
    
    def get_strategy_type(self) -> MoneyManagementType:
        return MoneyManagementType.MARTINGALE
    
    def calculate_position_size(
        self, 
        account_info: AccountInfo,
        symbol: str,
        entry_price: float,
        stop_loss: Optional[float],
        trade_history: list,
        market_data: Dict[str, Any]
    ) -> TradeParameters:
        """Calculate position size using martingale progression"""
        
        base_volume = self.config.get('base_volume', 0.01)
        max_multiplier = self.config.get('max_multiplier', 8)  # Max 8x base volume
        
        # Count consecutive losses
        consecutive_losses = self._count_consecutive_losses(trade_history, symbol)
        
        # Calculate multiplier (2^consecutive_losses, capped at max_multiplier)
        multiplier = min(2 ** consecutive_losses, max_multiplier)
        volume = base_volume * multiplier
        
        # Ensure volume doesn't exceed account limits
        max_volume_by_margin = account_info.free_margin / 1000  # Simplified calculation
        max_volume_by_balance = account_info.balance * 0.1 / 1000  # Max 10% of balance
        max_allowed_volume = min(max_volume_by_margin, max_volume_by_balance)
        
        volume = min(volume, max_allowed_volume)
        volume = max(volume, market_data.get('min_volume', 0.01))
        
        # Calculate risk amount
        risk_amount = 0
        if stop_loss:
            pip_value = market_data.get('pip_value', 1.0)
            pip_difference = abs(entry_price - stop_loss) / market_data.get('pip_size', 0.0001)
            risk_amount = volume * pip_difference * pip_value
        
        # Calculate confidence based on consecutive losses (lower after more losses)
        confidence_level = max(0.2, 1.0 - (consecutive_losses * 0.15))
        
        return TradeParameters(
            volume=volume,
            stop_loss=stop_loss,
            take_profit=None,
            risk_amount=risk_amount,
            max_loss=risk_amount,
            confidence_level=confidence_level
        )

    def get_ai_prompt(self, account_info: AccountInfo, trade_history: list) -> str:
        """Get AI prompt for martingale strategy"""

        recent_trades = trade_history[-15:] if len(trade_history) > 15 else trade_history
        consecutive_losses = self._count_consecutive_losses(trade_history, "current_symbol")
        win_rate = self._calculate_win_rate(recent_trades)
        max_drawdown = self._calculate_max_drawdown(recent_trades)

        base_volume = self.config.get('base_volume', 0.01)
        current_multiplier = min(2 ** consecutive_losses, self.config.get('max_multiplier', 8))
        current_volume = base_volume * current_multiplier

        prompt = f"""
You are managing a trading account using a MARTINGALE money management strategy.

ACCOUNT INFORMATION:
- Balance: ${account_info.balance:,.2f}
- Equity: ${account_info.equity:,.2f}
- Free Margin: ${account_info.free_margin:,.2f}
- Currency: {account_info.currency}
- Leverage: 1:{account_info.leverage}

MONEY MANAGEMENT STRATEGY: Martingale
- Base volume: {base_volume} lots
- Current consecutive losses: {consecutive_losses}
- Current multiplier: {current_multiplier}x
- Next trade volume: {current_volume} lots
- Maximum multiplier: {self.config.get('max_multiplier', 8)}x

RECENT PERFORMANCE:
- Win Rate: {win_rate:.1f}%
- Consecutive Losses: {consecutive_losses}
- Maximum Drawdown: {max_drawdown:.2f}%
- Total Recent Trades: {len(recent_trades)}

MARTINGALE STRATEGY GUIDELINES:
1. CRITICAL: This strategy requires HIGH win rate (>60%) to be profitable
2. Position size doubles after each loss to recover previous losses
3. ONE winning trade recovers ALL previous losses in the sequence
4. Maximum risk exposure increases exponentially with losses
5. Strategy resets to base volume after any winning trade

RISK WARNINGS:
- Current risk level: {"HIGH" if consecutive_losses >= 3 else "MODERATE" if consecutive_losses >= 1 else "LOW"}
- Consecutive losses create exponential risk growth
- Account can be wiped out with extended losing streak
- Maximum multiplier of {self.config.get('max_multiplier', 8)}x limits maximum exposure

STRATEGY RULES:
1. Only trade high-probability setups (>70% confidence)
2. Use tight stop losses to minimize individual trade risk
3. Avoid trading during high volatility/news events
4. NEVER override the calculated position size
5. Consider stopping strategy after {self.config.get('max_consecutive_losses', 4)} consecutive losses

CURRENT SITUATION ANALYSIS:
{"⚠️ WARNING: Multiple consecutive losses detected! Exercise extreme caution." if consecutive_losses >= 2 else "✅ Strategy operating within normal parameters."}

Based on the market data and current martingale position, provide your trading decisions with:
1. HIGH-PROBABILITY entry signals only (avoid marginal setups)
2. Tight stop loss levels to minimize risk per trade
3. Conservative take profit targets to ensure high win rate
4. Strict trade management rules
5. Assessment of market conditions for martingale suitability

Remember: Martingale requires discipline and high win rates. Quality over quantity is crucial.
Current position size is {current_volume} lots - ensure the setup justifies this risk level.
"""
        return prompt

    def _count_consecutive_losses(self, trade_history: list, symbol: str) -> int:
        """Count consecutive losses for the given symbol"""
        consecutive_losses = 0

        # Look at trades in reverse order (most recent first)
        for trade in reversed(trade_history):
            if trade.get('symbol') == symbol or symbol == "current_symbol":
                if trade.get('profit', 0) < 0:
                    consecutive_losses += 1
                else:
                    break  # Stop at first winning trade

        return consecutive_losses

    def _calculate_win_rate(self, trades: list) -> float:
        """Calculate win rate from trade history"""
        if not trades:
            return 0.0

        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        return (winning_trades / len(trades)) * 100

    def _calculate_max_drawdown(self, trades: list) -> float:
        """Calculate maximum drawdown percentage"""
        if not trades:
            return 0.0

        running_balance = 0
        peak_balance = 0
        max_drawdown = 0

        for trade in trades:
            running_balance += trade.get('profit', 0)
            peak_balance = max(peak_balance, running_balance)

            if peak_balance > 0:
                current_drawdown = ((peak_balance - running_balance) / peak_balance) * 100
                max_drawdown = max(max_drawdown, current_drawdown)

        return max_drawdown
