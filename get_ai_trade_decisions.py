#!/usr/bin/env python3
"""
Get AI trade management decisions for current positions and pending orders
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from mt5_integration.mt5_client import MT5<PERSON>lient
from ai_integration.qwen_client import Qwen<PERSON>lient
from account_management.models import TradingAccount
from money_management.base_strategy import AccountInfo
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

async def get_ai_decisions_for_trades():
    """Get AI decisions for current trades and pending orders"""
    print("🤖 GETTING AI TRADE MANAGEMENT DECISIONS")
    print("=" * 60)
    
    mt5_client = MT5Client()
    
    try:
        # Initialize MT5
        if not mt5_client.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        # Create account object
        account = TradingAccount(
            account_id="demo1",
            account_number=********,
            server="RoboForex-ECN",
            username="********",
            password="Daadb123",
            strategy_type="scalping",
            money_management_type="fixed_lot",
            symbols=["EURUSD", "GBPUSD", "USDJPY"],
            timeframes=["M15"]
        )
        
        if not mt5_client.login(account):
            print("❌ Failed to login")
            return False
        
        print("✅ MT5 connected and logged in")
        
        # Get current positions and orders
        positions = mt5_client.get_positions()
        orders = mt5_client.get_pending_orders()
        
        print(f"📊 Found {len(positions)} open positions and {len(orders)} pending orders")
        
        if not positions and not orders:
            print("⚠️ No positions or orders to analyze")
            return True
        
        # Get account info
        account_balance = mt5_client.get_account_info()
        account_info = AccountInfo(
            balance=account_balance.balance,
            equity=account_balance.equity,
            margin=account_balance.margin,
            free_margin=account_balance.free_margin,
            margin_level=account_balance.margin_level,
            currency=account_balance.currency,
            leverage=account_balance.leverage
        )
        
        # Initialize AI client
        async with QwenClient() as qwen_client:
            
            # Analyze each position
            if positions:
                print(f"\n🔍 ANALYZING {len(positions)} OPEN POSITIONS:")
                print("-" * 50)
                
                for i, position in enumerate(positions, 1):
                    await analyze_position(position, mt5_client, qwen_client, account_info, i)
            
            # Analyze each pending order
            if orders:
                print(f"\n🔍 ANALYZING {len(orders)} PENDING ORDERS:")
                print("-" * 50)
                
                for i, order in enumerate(orders, 1):
                    await analyze_order(order, mt5_client, qwen_client, account_info, i)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        logger.error(f"Analysis error: {e}", exc_info=True)
        return False
        
    finally:
        mt5_client.shutdown()

async def analyze_position(position, mt5_client, qwen_client, account_info, index):
    """Analyze a single position with AI"""
    try:
        symbol = position['symbol']
        print(f"\n{index}. POSITION ANALYSIS - {symbol}")
        print(f"   Type: {position['type']} | Volume: {position['volume']} | Entry: {position['price_open']:.5f}")
        print(f"   Current P&L: ${position['profit']:.2f}")
        
        # Get market data
        market_data = mt5_client.get_market_data(symbol, "M15", 50)
        if not market_data:
            print("   ❌ Failed to get market data")
            return
        
        # Build AI prompt for position management
        prompt = build_position_prompt(position, symbol, market_data, account_info)
        
        # Get AI decision
        print("   🤖 Getting AI decision...")
        ai_response = await qwen_client.generate_trading_decision(prompt)
        
        # Parse and display decision
        if isinstance(ai_response, dict):
            action = ai_response.get('action', 'UNKNOWN')
            confidence = ai_response.get('confidence', 0)
            reasoning = ai_response.get('reasoning', 'No reasoning provided')
            risk_level = ai_response.get('risk_level', 'UNKNOWN')
            
            print(f"   📋 AI DECISION: {action} (Confidence: {confidence:.1%})")
            print(f"   🎯 Risk Level: {risk_level}")
            print(f"   💭 Reasoning: {reasoning[:100]}...")
            
            if action in ['MODIFY_SL', 'MODIFY_TP', 'MODIFY_BOTH']:
                new_sl = ai_response.get('new_stop_loss')
                new_tp = ai_response.get('new_take_profit')
                if new_sl:
                    print(f"   📉 Suggested Stop Loss: {new_sl:.5f}")
                if new_tp:
                    print(f"   📈 Suggested Take Profit: {new_tp:.5f}")
        else:
            print(f"   📋 AI DECISION: {ai_response}")
            
    except Exception as e:
        print(f"   ❌ Error analyzing position: {e}")

async def analyze_order(order, mt5_client, qwen_client, account_info, index):
    """Analyze a single pending order with AI"""
    try:
        symbol = order['symbol']
        order_types = {2: "BUY_LIMIT", 3: "SELL_LIMIT", 4: "BUY_STOP", 5: "SELL_STOP"}
        order_type = order_types.get(order['type'], f"TYPE_{order['type']}")
        
        print(f"\n{index}. ORDER ANALYSIS - {symbol}")
        print(f"   Type: {order_type} | Volume: {order['volume']} | Price: {order['price_open']:.5f}")
        print(f"   Ticket: {order['ticket']}")
        
        # Get market data
        market_data = mt5_client.get_market_data(symbol, "M15", 50)
        if not market_data:
            print("   ❌ Failed to get market data")
            return
        
        # Build AI prompt for order management
        prompt = build_order_prompt(order, symbol, market_data, account_info)
        
        # Get AI decision
        print("   🤖 Getting AI decision...")
        ai_response = await qwen_client.generate_trading_decision(prompt)
        
        # Parse and display decision
        if isinstance(ai_response, dict):
            action = ai_response.get('action', 'UNKNOWN')
            confidence = ai_response.get('confidence', 0)
            reasoning = ai_response.get('reasoning', 'No reasoning provided')
            risk_level = ai_response.get('risk_level', 'UNKNOWN')
            
            print(f"   📋 AI DECISION: {action} (Confidence: {confidence:.1%})")
            print(f"   🎯 Risk Level: {risk_level}")
            print(f"   💭 Reasoning: {reasoning[:100]}...")
            
            if action in ['MODIFY_PRICE', 'MODIFY_SL', 'MODIFY_TP', 'MODIFY_ALL']:
                new_price = ai_response.get('new_price')
                new_sl = ai_response.get('new_stop_loss')
                new_tp = ai_response.get('new_take_profit')
                if new_price:
                    print(f"   💰 Suggested Price: {new_price:.5f}")
                if new_sl:
                    print(f"   📉 Suggested Stop Loss: {new_sl:.5f}")
                if new_tp:
                    print(f"   📈 Suggested Take Profit: {new_tp:.5f}")
        else:
            print(f"   📋 AI DECISION: {ai_response}")
            
    except Exception as e:
        print(f"   ❌ Error analyzing order: {e}")

def build_position_prompt(position, symbol, market_data, account_info):
    """Build AI prompt for position analysis"""
    current_price = market_data['current_price']
    entry_price = position['price_open']
    
    if position['type'] == 'BUY':
        unrealized_pips = (current_price - entry_price) / market_data.get('pip_size', 0.0001)
    else:
        unrealized_pips = (entry_price - current_price) / market_data.get('pip_size', 0.0001)
    
    return f"""
POSITION MANAGEMENT ANALYSIS

ACCOUNT: Balance: ${account_info.balance:.2f} | Equity: ${account_info.equity:.2f}

POSITION: {symbol} {position['type']} {position['volume']} @ {entry_price:.5f}
Current Price: {current_price:.5f} | P&L: ${position['profit']:.2f} | Pips: {unrealized_pips:.1f}
Stop Loss: {position.get('sl', 0):.5f} | Take Profit: {position.get('tp', 0):.5f}

MARKET: Spread: {market_data.get('spread', 0):.1f} pips | Volatility: {market_data.get('volatility', 0):.5f}

Decide: HOLD, CLOSE, MODIFY_SL, MODIFY_TP, or MODIFY_BOTH

Respond in JSON: {{"action": "...", "confidence": 0.8, "reasoning": "...", "risk_level": "LOW|MEDIUM|HIGH", "new_stop_loss": null, "new_take_profit": null}}
"""

def build_order_prompt(order, symbol, market_data, account_info):
    """Build AI prompt for order analysis"""
    current_price = market_data['current_price']
    order_price = order['price_open']
    
    distance_pips = abs(order_price - current_price) / market_data.get('pip_size', 0.0001)
    
    order_types = {2: "BUY_LIMIT", 3: "SELL_LIMIT", 4: "BUY_STOP", 5: "SELL_STOP"}
    order_type = order_types.get(order['type'], f"TYPE_{order['type']}")
    
    return f"""
PENDING ORDER MANAGEMENT ANALYSIS

ACCOUNT: Balance: ${account_info.balance:.2f} | Equity: ${account_info.equity:.2f}

ORDER: {symbol} {order_type} {order['volume']} @ {order_price:.5f}
Current Price: {current_price:.5f} | Distance: {distance_pips:.1f} pips
Stop Loss: {order.get('sl', 0):.5f} | Take Profit: {order.get('tp', 0):.5f}

MARKET: Spread: {market_data.get('spread', 0):.1f} pips | Volatility: {market_data.get('volatility', 0):.5f}

Decide: KEEP, CANCEL, MODIFY_PRICE, MODIFY_SL, MODIFY_TP, or MODIFY_ALL

Respond in JSON: {{"action": "...", "confidence": 0.8, "reasoning": "...", "risk_level": "LOW|MEDIUM|HIGH", "new_price": null, "new_stop_loss": null, "new_take_profit": null}}
"""

async def main():
    """Main function"""
    success = await get_ai_decisions_for_trades()
    
    if success:
        print("\n🎉 AI TRADE ANALYSIS COMPLETED")
        print("\nThe AI has analyzed all current positions and pending orders.")
        print("Decisions are based on current market conditions, account status, and risk management.")
    else:
        print("\n❌ AI TRADE ANALYSIS FAILED")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
