#!/usr/bin/env python3
"""
Risk Management Compliance Testing
Tests that the system properly enforces daily loss limits and account-specific risk settings
"""

import sys
import os
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add src to path
sys.path.append('src')

from signal_generation.signal_generator import SignalGenerator
from account_management.account_manager import Account<PERSON>anager
from mt5_integration.mt5_client import MT5Client
from money_management.base_strategy import AccountInfo

class RiskManagementValidator:
    """Validates risk management compliance"""
    
    def __init__(self):
        self.test_results = []
        
    async def run_comprehensive_validation(self):
        """Run comprehensive risk management validation"""
        print("🛡️ RISK MANAGEMENT COMPLIANCE VALIDATION")
        print("=" * 60)
        
        # Test scenarios
        await self._test_daily_loss_limit_enforcement()
        await self._test_account_specific_risk_settings()
        await self._test_trade_risk_limits()
        await self._test_position_limits()
        
        # Analyze results
        self._analyze_results()
        
    async def _test_daily_loss_limit_enforcement(self):
        """Test daily loss limit enforcement"""
        print("\n📉 Testing Daily Loss Limit Enforcement")
        print("-" * 40)
        
        # Create signal generator for testing
        signal_gen = SignalGenerator()
        
        # Test account from accounts.json
        test_account = {
            'account_id': 'demo1',
            'account_number': ********,
            'money_management_settings': {
                'max_daily_loss': 3.0,  # $3 limit
                'max_daily_trades': 5,
                'max_open_positions': 2
            }
        }
        
        # Simulate different daily loss scenarios
        loss_scenarios = [
            {"daily_loss": 0.0, "should_allow": True, "description": "No loss"},
            {"daily_loss": -1.5, "should_allow": True, "description": "Small loss within limit"},
            {"daily_loss": -2.9, "should_allow": True, "description": "Near limit but within"},
            {"daily_loss": -3.0, "should_allow": False, "description": "Exactly at limit"},
            {"daily_loss": -3.5, "should_allow": False, "description": "Exceeds limit"},
            {"daily_loss": -10.0, "should_allow": False, "description": "Far exceeds limit"}
        ]
        
        for scenario in loss_scenarios:
            # Mock the daily PnL
            today_key = datetime.now().strftime('%Y-%m-%d')
            daily_key = f"{test_account['account_id']}_{today_key}"
            signal_gen.daily_pnl[daily_key] = scenario['daily_loss']
            
            # Test risk limit check
            can_trade = signal_gen._check_risk_limits(test_account)
            
            print(f"  {scenario['description']}: Loss ${abs(scenario['daily_loss']):.1f}")
            print(f"    Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
            print(f"    Actual: {'ALLOW' if can_trade else 'BLOCK'}")
            
            # Validate result
            is_correct = can_trade == scenario['should_allow']
            print(f"    Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
            
            self.test_results.append({
                'test': 'daily_loss_limit',
                'scenario': scenario['description'],
                'expected': scenario['should_allow'],
                'actual': can_trade,
                'passed': is_correct
            })
            
    async def _test_account_specific_risk_settings(self):
        """Test account-specific risk settings validation"""
        print("\n⚙️ Testing Account-Specific Risk Settings")
        print("-" * 40)
        
        # Load actual account settings
        account_manager = AccountManager()
        if account_manager.load_accounts():
            for account_id, account in account_manager.accounts.items():
                print(f"  Account: {account_id}")
                
                # Check if risk settings are loaded
                risk_settings = account.money_management_settings
                print(f"    Risk Percent: {risk_settings.get('risk_percent', 'NOT SET')}%")
                print(f"    Max Daily Loss: ${risk_settings.get('max_daily_loss', 'NOT SET')}")
                print(f"    Max Daily Trades: {risk_settings.get('max_daily_trades', 'NOT SET')}")
                print(f"    Max Open Positions: {risk_settings.get('max_open_positions', 'NOT SET')}")
                
                # Validate required settings exist
                required_settings = ['risk_percent', 'max_daily_loss', 'max_daily_trades', 'max_open_positions']
                missing_settings = [setting for setting in required_settings if setting not in risk_settings]
                
                if missing_settings:
                    print(f"    ❌ Missing settings: {missing_settings}")
                    self.test_results.append({
                        'test': 'account_settings',
                        'account': account_id,
                        'passed': False,
                        'missing': missing_settings
                    })
                else:
                    print(f"    ✅ All required settings present")
                    self.test_results.append({
                        'test': 'account_settings',
                        'account': account_id,
                        'passed': True
                    })
        else:
            print("  ❌ Failed to load accounts")
            
    async def _test_trade_risk_limits(self):
        """Test individual trade risk limits"""
        print("\n💰 Testing Trade Risk Limits")
        print("-" * 40)
        
        signal_gen = SignalGenerator()
        
        # Mock account info
        account_info = AccountInfo(
            balance=74.40,
            equity=74.40,
            margin=0.0,
            free_margin=74.40,
            margin_level=0.0,
            currency="USD",
            leverage=500
        )
        
        test_account = {
            'account_id': 'demo1',
            'money_management_settings': {
                'risk_percent': 2.0,
                'max_risk_per_trade': 10.0,  # 10% max per trade
                'max_risk_multiplier': 10.0
            }
        }
        
        # Mock trade parameters with different risk amounts
        class MockTradeParams:
            def __init__(self, risk_amount):
                self.risk_amount = risk_amount
                self.volume = 0.01
        
        risk_scenarios = [
            {"risk_amount": 1.49, "should_allow": True, "description": "Normal 2% risk"},
            {"risk_amount": 7.44, "should_allow": True, "description": "10% risk (at limit)"},
            {"risk_amount": 8.00, "should_allow": False, "description": "Exceeds 10% limit"},
            {"risk_amount": 15.00, "should_allow": False, "description": "Far exceeds limit"}
        ]
        
        for scenario in risk_scenarios:
            trade_params = MockTradeParams(scenario['risk_amount'])
            can_trade = signal_gen._check_trade_risk_limits(test_account, trade_params, account_info)
            
            print(f"  {scenario['description']}: Risk ${scenario['risk_amount']:.2f}")
            print(f"    Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
            print(f"    Actual: {'ALLOW' if can_trade else 'BLOCK'}")
            
            is_correct = can_trade == scenario['should_allow']
            print(f"    Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
            
            self.test_results.append({
                'test': 'trade_risk_limits',
                'scenario': scenario['description'],
                'expected': scenario['should_allow'],
                'actual': can_trade,
                'passed': is_correct
            })
            
    async def _test_position_limits(self):
        """Test position and order limits"""
        print("\n📊 Testing Position and Order Limits")
        print("-" * 40)
        
        # This would require MT5 connection to test properly
        # For now, we'll test the logic
        
        signal_gen = SignalGenerator()
        
        test_account = {
            'account_id': 'demo1',
            'money_management_settings': {
                'max_open_positions': 2,
                'max_pending_orders': 4
            }
        }
        
        # Mock position counts
        position_scenarios = [
            {"open_positions": 0, "pending_orders": 0, "should_allow": True},
            {"open_positions": 1, "pending_orders": 2, "should_allow": True},
            {"open_positions": 2, "pending_orders": 3, "should_allow": False},  # At position limit
            {"open_positions": 1, "pending_orders": 4, "should_allow": False},  # At order limit
        ]
        
        for scenario in position_scenarios:
            print(f"  Positions: {scenario['open_positions']}, Orders: {scenario['pending_orders']}")
            print(f"    Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
            
            # Note: This test would need MT5 connection for full validation
            # For now, we document the expected behavior
            self.test_results.append({
                'test': 'position_limits',
                'scenario': f"Pos:{scenario['open_positions']}, Ord:{scenario['pending_orders']}",
                'expected': scenario['should_allow'],
                'note': 'Requires MT5 connection for full test'
            })
            
    def _analyze_results(self):
        """Analyze risk management test results"""
        print("\n🔍 RISK MANAGEMENT ANALYSIS")
        print("=" * 60)
        
        # Group results by test type
        test_groups = {}
        for result in self.test_results:
            test_type = result['test']
            if test_type not in test_groups:
                test_groups[test_type] = []
            test_groups[test_type].append(result)
        
        overall_pass = True
        
        for test_type, results in test_groups.items():
            print(f"\n📈 {test_type.replace('_', ' ').title()}:")
            
            passed_tests = [r for r in results if r.get('passed', False)]
            total_tests = len(results)
            pass_rate = len(passed_tests) / total_tests if total_tests > 0 else 0
            
            print(f"  Passed: {len(passed_tests)}/{total_tests} ({pass_rate:.1%})")
            
            if pass_rate < 1.0:
                overall_pass = False
                failed_tests = [r for r in results if not r.get('passed', False)]
                for failed in failed_tests:
                    print(f"    ❌ {failed.get('scenario', 'Unknown')}")
            else:
                print(f"    ✅ All tests passed")
        
        print(f"\n🎯 OVERALL RESULT: {'✅ PASS' if overall_pass else '❌ FAIL'}")
        return overall_pass

async def main():
    """Main test function"""
    validator = RiskManagementValidator()
    
    try:
        await validator.run_comprehensive_validation()
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(main())
