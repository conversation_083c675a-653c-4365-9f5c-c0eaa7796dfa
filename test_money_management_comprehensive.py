#!/usr/bin/env python3
"""
Comprehensive test script for money management system
Tests all risk calculations, position sizing, and validation logic
"""

import sys
import json
sys.path.append('src')

from money_management.percent_risk import PercentRiskStrategy
from money_management.fixed_volume import FixedVolumeStrategy
from money_management.martingale import MartingaleStrategy
from money_management.anti_martingale import AntiMartingaleStrategy
from money_management.base_strategy import AccountInfo

def test_percent_risk_strategy():
    """Test percent risk strategy with various scenarios"""
    print("=" * 80)
    print("TESTING PERCENT RISK STRATEGY")
    print("=" * 80)
    
    # Test scenarios
    scenarios = [
        {
            "name": "Conservative (0.5% risk, $100 balance)",
            "config": {"risk_percent": 0.5, "max_risk_multiplier": 2.0},
            "account": AccountInfo(100, 100, 0, 50, 1000, "USD", 100),
            "entry_price": 1.0850,
            "stop_loss": 1.0820,  # 30 pips
            "expected_volume": 0.0017,  # Should be very small
        },
        {
            "name": "Moderate (1.5% risk, $1000 balance)",
            "config": {"risk_percent": 1.5, "max_risk_multiplier": 3.0},
            "account": AccountInfo(1000, 1000, 0, 500, 2000, "USD", 100),
            "entry_price": 1.0850,
            "stop_loss": 1.0820,  # 30 pips
            "expected_volume": 0.05,  # Should be reasonable
        },
        {
            "name": "Aggressive (3% risk, $5000 balance)",
            "config": {"risk_percent": 3.0, "max_risk_multiplier": 4.0},
            "account": AccountInfo(5000, 5000, 0, 2500, 5000, "USD", 100),
            "entry_price": 1.0850,
            "stop_loss": 1.0820,  # 30 pips
            "expected_volume": 0.5,  # Should be larger
        },
        {
            "name": "Small SL (0.5% risk, 10 pips)",
            "config": {"risk_percent": 0.5, "max_risk_multiplier": 2.0},
            "account": AccountInfo(100, 100, 0, 50, 1000, "USD", 100),
            "entry_price": 1.0850,
            "stop_loss": 1.0840,  # 10 pips
            "expected_volume": 0.005,  # Should be larger than 30-pip scenario
        }
    ]
    
    market_data = {
        "pip_value": 10.0,  # $10 per pip per lot for EURUSD
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        print("-" * 60)
        
        strategy = PercentRiskStrategy(scenario['config'])
        trade_params = strategy.calculate_position_size(
            account_info=scenario['account'],
            symbol="EURUSD",
            entry_price=scenario['entry_price'],
            stop_loss=scenario['stop_loss'],
            trade_history=[],
            market_data=market_data
        )
        
        # Calculate expected values
        risk_amount = scenario['account'].balance * (scenario['config']['risk_percent'] / 100)
        pip_difference = abs(scenario['entry_price'] - scenario['stop_loss']) / market_data['pip_size']
        calculated_volume = risk_amount / (pip_difference * market_data['pip_value'])
        
        print(f"  Account Balance: ${scenario['account'].balance}")
        print(f"  Risk Percent: {scenario['config']['risk_percent']}%")
        print(f"  Expected Risk Amount: ${risk_amount:.2f}")
        print(f"  Stop Loss Distance: {pip_difference:.1f} pips")
        print(f"  Calculated Volume: {calculated_volume:.6f} lots")
        print(f"  Final Volume: {trade_params.volume:.6f} lots")
        print(f"  Actual Risk: ${trade_params.risk_amount:.2f}")
        print(f"  Risk Multiplier: {trade_params.risk_amount / risk_amount:.2f}x")
        print(f"  Confidence Level: {trade_params.confidence_level:.2f}")
        
        # Validate results
        if trade_params.volume == 0:
            print(f"  ⚠️  TRADE SKIPPED - Risk too high for minimum volume")
        elif trade_params.risk_amount > risk_amount * scenario['config']['max_risk_multiplier']:
            print(f"  ❌ RISK EXCEEDED - Actual risk exceeds maximum allowed")
        else:
            print(f"  ✅ RISK ACCEPTABLE")

def test_fixed_volume_strategy():
    """Test fixed volume strategy"""
    print("\n" + "=" * 80)
    print("TESTING FIXED VOLUME STRATEGY")
    print("=" * 80)
    
    config = {"fixed_volume": 0.02}
    account = AccountInfo(1000, 1000, 0, 500, 2000, "USD", 100)
    
    market_data = {
        "pip_value": 10.0,
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    strategy = FixedVolumeStrategy(config)
    trade_params = strategy.calculate_position_size(
        account_info=account,
        symbol="EURUSD",
        entry_price=1.0850,
        stop_loss=1.0820,
        trade_history=[],
        market_data=market_data
    )
    
    print(f"  Fixed Volume: {config['fixed_volume']} lots")
    print(f"  Calculated Volume: {trade_params.volume} lots")
    print(f"  Risk Amount: ${trade_params.risk_amount:.2f}")
    print(f"  Risk as % of balance: {(trade_params.risk_amount / account.balance) * 100:.2f}%")

def test_risk_validation():
    """Test risk validation logic"""
    print("\n" + "=" * 80)
    print("TESTING RISK VALIDATION")
    print("=" * 80)
    
    # Test scenarios that should be rejected
    test_cases = [
        {
            "name": "Excessive risk (10% of balance)",
            "balance": 100,
            "risk_amount": 10,
            "should_pass": False
        },
        {
            "name": "Acceptable risk (1% of balance)",
            "balance": 100,
            "risk_amount": 1,
            "should_pass": True
        },
        {
            "name": "Borderline risk (5% of balance)",
            "balance": 100,
            "risk_amount": 5,
            "should_pass": True
        }
    ]
    
    for case in test_cases:
        print(f"\n{case['name']}:")
        print(f"  Balance: ${case['balance']}")
        print(f"  Risk Amount: ${case['risk_amount']}")
        print(f"  Risk Percentage: {(case['risk_amount'] / case['balance']) * 100:.1f}%")
        
        # Simple validation (max 50% of balance)
        max_allowed = case['balance'] * 0.5
        passes = case['risk_amount'] <= max_allowed
        
        print(f"  Max Allowed Risk: ${max_allowed}")
        print(f"  Result: {'✅ PASS' if passes else '❌ FAIL'}")
        print(f"  Expected: {'✅ PASS' if case['should_pass'] else '❌ FAIL'}")

if __name__ == "__main__":
    print("COMPREHENSIVE MONEY MANAGEMENT TESTING")
    print("=" * 80)
    
    test_percent_risk_strategy()
    test_fixed_volume_strategy()
    test_risk_validation()
    
    print("\n" + "=" * 80)
    print("TESTING COMPLETED")
    print("=" * 80)
