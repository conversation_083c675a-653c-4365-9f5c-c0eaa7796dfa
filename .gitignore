# Sensitive Files - NEVER COMMIT THESE
.env
*.env
.env.local
.env.production
config/accounts.json

# API Keys and Credentials
**/api_keys.json
**/credentials.json
**/secrets.json

# Trading Data and Logs
logs/
*.log
trading_data/
backtest_results/
live_trading_results/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# MetaTrader 5 Files
*.ex5
*.mq5
MQL5/
Experts/
Indicators/
Scripts/
Include/
Libraries/

# Temporary Files
*.tmp
*.temp
temp/
tmp/

# Database Files
*.db
*.sqlite
*.sqlite3

# Backup Files
*.bak
*.backup
backup/

# Test Files (keep test scripts but exclude test data)
test_data/
test_results/
*.test.json

# Documentation Build
docs/_build/
site/

# Coverage Reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
