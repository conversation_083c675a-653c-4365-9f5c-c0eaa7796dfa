#!/usr/bin/env python3
"""
Real Account Login and Trade Execution Test
Tests login to CapitalxtendLLC-MU account and executes a quick EURUSD trade
"""

import sys
import os
import json
import time
from datetime import datetime
from typing import Optional

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from mt5_integration.mt5_client import MT5Client
from account_management.models import TradingAccount
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

def load_real_account() -> Optional[TradingAccount]:
    """Load the real account from config"""
    try:
        with open('config/accounts.json', 'r') as f:
            accounts_config = json.load(f)
        
        account_data = accounts_config['accounts'][0]
        return TradingAccount(
            account_id=account_data['account_id'],
            account_number=account_data['account_number'],
            server=account_data['server'],
            username=account_data.get('username', ''),
            password=account_data['password'],
            strategy_type=account_data.get('strategy', ''),
            money_management_type=account_data.get('money_management', ''),
            symbols=account_data.get('symbols', []),
            timeframes=['H1'],
            money_management_config=account_data.get('money_management_settings', {})
        )
    except Exception as e:
        logger.error(f"Error loading real account: {e}")
        return None

def test_login_and_account_info(client: MT5Client, account: TradingAccount) -> bool:
    """Test login and get account information"""
    print("\n" + "="*60)
    print("🔐 TESTING REAL ACCOUNT LOGIN")
    print("="*60)
    print(f"Account: {account.account_number}")
    print(f"Server: {account.server}")
    
    # Test login
    if not client.login(account):
        print("❌ LOGIN FAILED")
        return False
    
    print("✅ LOGIN SUCCESS")
    
    # Get account info
    account_info = client.get_account_info()
    if not account_info:
        print("❌ Failed to get account info")
        return False
    
    print(f"✅ Account Info Retrieved:")
    print(f"   💰 Balance: ${account_info.balance:.2f}")
    print(f"   💰 Equity: ${account_info.equity:.2f}")
    print(f"   💰 Free Margin: ${account_info.free_margin:.2f}")
    print(f"   💰 Currency: {account_info.currency}")
    print(f"   💰 Leverage: 1:{account_info.leverage}")
    
    return True

def test_market_data(client: MT5Client) -> bool:
    """Test EURUSD! market data retrieval"""
    print("\n" + "="*60)
    print("📈 TESTING EURUSD! MARKET DATA")
    print("="*60)

    market_data = client.get_market_data('EURUSD!', 'H1', 10)
    if not market_data:
        print("❌ Failed to get EURUSD! market data")
        return False

    print("✅ EURUSD! Market Data Retrieved:")
    print(f"   📊 Current Price: {market_data['current_price']:.5f}")
    print(f"   📊 Spread: {market_data['spread']:.1f} pips")
    print(f"   📊 Min Volume: {market_data['min_volume']}")
    print(f"   📊 Max Volume: {market_data['max_volume']}")

    return True

def test_trade_execution(client: MT5Client) -> bool:
    """Test opening and closing a 0.01 EURUSD! position"""
    print("\n" + "="*60)
    print("🚀 TESTING TRADE EXECUTION")
    print("="*60)

    symbol = "EURUSD!"
    volume = 0.01
    
    # Step 1: Open BUY position
    print(f"📈 Opening BUY {volume} {symbol}...")
    
    order_id = client.place_order(
        symbol=symbol,
        action="BUY",
        volume=volume,
        magic_number=123456,
        comment="Test Trade"
    )
    
    if not order_id:
        print("❌ Failed to open position")
        return False
    
    print(f"✅ Position opened successfully! Order ID: {order_id}")
    
    # Step 2: Wait a moment
    print("⏳ Waiting 3 seconds...")
    time.sleep(3)
    
    # Step 3: Check positions
    positions = client.get_positions()
    target_position = None
    
    for pos in positions:
        if pos['magic'] == 123456 and pos['symbol'] == symbol:
            target_position = pos
            break
    
    if not target_position:
        print("❌ Position not found in open positions")
        return False
    
    print(f"✅ Position found:")
    print(f"   📊 Ticket: {target_position['ticket']}")
    print(f"   📊 Symbol: {target_position['symbol']}")
    print(f"   📊 Type: {target_position['type']}")
    print(f"   📊 Volume: {target_position['volume']}")
    print(f"   📊 Open Price: {target_position['price_open']:.5f}")
    print(f"   📊 Current Price: {target_position['price_current']:.5f}")
    print(f"   📊 Profit: ${target_position['profit']:.2f}")
    
    # Step 4: Close position
    print(f"📉 Closing position {target_position['ticket']}...")
    
    if client.close_position(target_position['ticket']):
        print("✅ Position closed successfully!")
        
        # Verify position is closed
        time.sleep(2)
        positions_after = client.get_positions()
        still_open = any(pos['ticket'] == target_position['ticket'] for pos in positions_after)
        
        if not still_open:
            print("✅ Position confirmed closed")
            return True
        else:
            print("⚠️  Position still appears open")
            return False
    else:
        print("❌ Failed to close position")
        return False

def main():
    """Main test function"""
    print("🚀 TESTING REAL ACCOUNT EXECUTION")
    print("Account: ******** @ CapitalxtendLLC-MU")
    print("=" * 80)
    
    # Load real account
    account = load_real_account()
    if not account:
        print("❌ Failed to load real account configuration")
        return
    
    # Initialize MT5
    client = MT5Client()
    if not client.initialize():
        print("❌ Failed to initialize MT5")
        return
    
    try:
        # Test login and account info
        if not test_login_and_account_info(client, account):
            return
        
        # Test market data
        if not test_market_data(client):
            return
        
        # Test trade execution
        if not test_trade_execution(client):
            return
        
        print("\n" + "="*80)
        print("🎉 ALL TESTS PASSED!")
        print("✅ Login successful")
        print("✅ Account info retrieved")
        print("✅ Market data accessible")
        print("✅ Trade execution working")
        print("✅ Position management working")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ EXCEPTION OCCURRED: {e}")
        logger.error(f"Test exception: {e}")
        
    finally:
        # Cleanup
        client.shutdown()
        print("\n🔒 MT5 connection closed")

if __name__ == "__main__":
    main()
