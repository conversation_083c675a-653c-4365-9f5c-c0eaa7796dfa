#!/usr/bin/env python3
"""
Debug test for account loading
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from account_management.account_manager import Account<PERSON><PERSON><PERSON>

def test_account_loading():
    """Debug account loading"""
    
    # Create test config
    test_config = {
        "accounts": [
            {
                "account_id": "test_account_1",
                "account_number": ********,
                "password": "test_password",
                "server": "RoboForex-ECN",
                "strategy": "trend_following",
                "money_management": "percent_risk",
                "symbols": [
                    {"symbol": "EURUSD", "timeframe": "H1"}
                ],
                "money_management_settings": {
                    "risk_percent": 2.0
                }
            }
        ]
    }
    
    # Create temp file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_config, f)
        config_file = f.name
    
    try:
        # Test loading
        account_manager = AccountManager(config_path=config_file)
        result = account_manager.load_accounts()
        
        print(f"Load result: {result}")
        print(f"Number of accounts: {len(account_manager.accounts)}")
        
        if account_manager.accounts:
            for account_id, account in account_manager.accounts.items():
                print(f"Account: {account_id}")
                print(f"  Number: {account.account_number}")
                print(f"  Strategy: {account.strategy_type}")
                print(f"  Money Management: {account.money_management_type}")
                print(f"  Symbols: {account.symbols}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        os.unlink(config_file)

if __name__ == "__main__":
    test_account_loading()
