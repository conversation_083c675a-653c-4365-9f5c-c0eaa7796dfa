"""
Account Switch Diagnostics
Comprehensive diagnostics for account switching issues
"""

import asyncio
import MetaTrader5 as mt5
from typing import Dict, Any, List, Optional
from datetime import datetime
import time

from logging_system.logger import get_logger
from account_management.models import TradingAccount

logger = get_logger(__name__)


class AccountSwitchDiagnostics:
    """Diagnoses account switching issues"""
    
    def __init__(self):
        self.diagnostic_history = []
        self.last_successful_account = None
        self.switch_attempts = 0
        self.successful_switches = 0
        
    async def diagnose_account_switch(self, from_account: Optional[TradingAccount], to_account: TradingAccount) -> Dict[str, Any]:
        """Comprehensive diagnosis of account switching"""
        self.switch_attempts += 1
        
        logger.info(f"🔍 ACCOUNT_SWITCH_DIAG: Starting diagnosis #{self.switch_attempts}")
        logger.info(f"🔍 ACCOUNT_SWITCH_DIAG: From: {from_account.account_number if from_account else 'None'} -> To: {to_account.account_number}")
        
        diagnosis = {
            'timestamp': datetime.now().isoformat(),
            'switch_attempt': self.switch_attempts,
            'from_account': from_account.account_number if from_account else None,
            'to_account': to_account.account_number,
            'pre_switch_state': {},
            'switch_process': {},
            'post_switch_state': {},
            'issues_found': [],
            'recommendations': [],
            'success': False
        }
        
        try:
            # Pre-switch diagnostics
            diagnosis['pre_switch_state'] = await self._diagnose_pre_switch_state(from_account)
            
            # Switch process diagnostics
            diagnosis['switch_process'] = await self._diagnose_switch_process(to_account)
            
            # Post-switch diagnostics
            diagnosis['post_switch_state'] = await self._diagnose_post_switch_state(to_account)
            
            # Analyze results
            diagnosis['issues_found'] = self._analyze_issues(diagnosis)
            diagnosis['recommendations'] = self._generate_recommendations(diagnosis)
            diagnosis['success'] = len(diagnosis['issues_found']) == 0
            
            if diagnosis['success']:
                self.successful_switches += 1
                self.last_successful_account = to_account
                logger.info(f"✅ ACCOUNT_SWITCH_DIAG: Switch successful ({self.successful_switches}/{self.switch_attempts})")
            else:
                logger.error(f"❌ ACCOUNT_SWITCH_DIAG: Switch failed - {len(diagnosis['issues_found'])} issues found")
                for issue in diagnosis['issues_found']:
                    logger.error(f"❌ ACCOUNT_SWITCH_DIAG: Issue: {issue}")
            
        except Exception as e:
            diagnosis['issues_found'].append(f"Exception during diagnosis: {e}")
            logger.error(f"❌ ACCOUNT_SWITCH_DIAG: Exception: {e}")
        
        self.diagnostic_history.append(diagnosis)
        return diagnosis
    
    async def _diagnose_pre_switch_state(self, current_account: Optional[TradingAccount]) -> Dict[str, Any]:
        """Diagnose state before switching accounts"""
        state = {
            'mt5_initialized': False,
            'terminal_connected': False,
            'current_account_logged_in': False,
            'current_account_number': None,
            'trading_allowed': False,
            'expert_allowed': False,
            'terminal_build': None,
            'terminal_company': None
        }
        
        try:
            # Check MT5 initialization
            terminal_info = mt5.terminal_info()
            if terminal_info is not None:
                state['mt5_initialized'] = True
                state['terminal_connected'] = terminal_info.connected
                state['terminal_build'] = terminal_info.build
                state['terminal_company'] = terminal_info.company
                logger.info(f"🔍 PRE_SWITCH: Terminal connected: {terminal_info.connected}, Build: {terminal_info.build}")
            else:
                logger.warning("⚠️ PRE_SWITCH: Cannot get terminal info")
            
            # Check current account
            account_info = mt5.account_info()
            if account_info is not None:
                state['current_account_logged_in'] = True
                state['current_account_number'] = account_info.login
                state['trading_allowed'] = account_info.trade_allowed
                state['expert_allowed'] = account_info.trade_expert
                logger.info(f"🔍 PRE_SWITCH: Current account: {account_info.login}, Trading: {account_info.trade_allowed}, Expert: {account_info.trade_expert}")
                
                # Verify account matches expected
                if current_account and account_info.login != current_account.account_number:
                    logger.warning(f"⚠️ PRE_SWITCH: Account mismatch - expected {current_account.account_number}, got {account_info.login}")
            else:
                logger.warning("⚠️ PRE_SWITCH: No account logged in")
                
        except Exception as e:
            logger.error(f"❌ PRE_SWITCH: Exception: {e}")
            state['error'] = str(e)
        
        return state
    
    async def _diagnose_switch_process(self, target_account: TradingAccount) -> Dict[str, Any]:
        """Diagnose the account switching process"""
        process = {
            'login_attempt_time': None,
            'login_duration': None,
            'login_successful': False,
            'login_error': None,
            'server_reachable': False,
            'credentials_valid': False,
            'retry_attempts': 0
        }
        
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            process['retry_attempts'] = attempt + 1
            logger.info(f"🔍 SWITCH_PROCESS: Login attempt {attempt + 1}/{max_retries}")
            
            try:
                start_time = time.time()
                process['login_attempt_time'] = datetime.now().isoformat()
                
                # Attempt login
                login_result = mt5.login(
                    login=target_account.account_number,
                    password=target_account.password,
                    server=target_account.server
                )
                
                end_time = time.time()
                process['login_duration'] = end_time - start_time
                
                if login_result:
                    process['login_successful'] = True
                    process['server_reachable'] = True
                    process['credentials_valid'] = True
                    logger.info(f"✅ SWITCH_PROCESS: Login successful in {process['login_duration']:.2f}s")
                    break
                else:
                    error = mt5.last_error()
                    process['login_error'] = error
                    logger.error(f"❌ SWITCH_PROCESS: Login failed - {error}")
                    
                    # Analyze error
                    if error and 'server' in str(error).lower():
                        process['server_reachable'] = False
                    elif error and ('password' in str(error).lower() or 'login' in str(error).lower()):
                        process['credentials_valid'] = False
                    
                    if attempt < max_retries - 1:
                        logger.info(f"🔄 SWITCH_PROCESS: Retrying in {retry_delay}s...")
                        await asyncio.sleep(retry_delay)
                
            except Exception as e:
                process['login_error'] = str(e)
                logger.error(f"❌ SWITCH_PROCESS: Exception on attempt {attempt + 1}: {e}")
                
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
        
        return process
    
    async def _diagnose_post_switch_state(self, target_account: TradingAccount) -> Dict[str, Any]:
        """Diagnose state after switching accounts"""
        state = {
            'account_verified': False,
            'account_number_match': False,
            'trading_permissions': {},
            'account_balance': None,
            'symbols_available': 0,
            'market_data_accessible': False,
            'order_placement_test': False
        }
        
        try:
            # Verify account login
            account_info = mt5.account_info()
            if account_info is not None:
                state['account_verified'] = True
                state['account_number_match'] = account_info.login == target_account.account_number
                state['account_balance'] = account_info.balance
                
                state['trading_permissions'] = {
                    'trade_allowed': account_info.trade_allowed,
                    'trade_expert': account_info.trade_expert,
                    'margin_mode': account_info.margin_mode,
                    'currency': account_info.currency,
                    'leverage': account_info.leverage
                }
                
                logger.info(f"🔍 POST_SWITCH: Account verified: {target_account.account_number}, Balance: {account_info.balance}")
            else:
                logger.error("❌ POST_SWITCH: Cannot get account info after switch")
            
            # Test symbol access
            symbols = mt5.symbols_get()
            if symbols:
                state['symbols_available'] = len(symbols)
                logger.info(f"🔍 POST_SWITCH: {len(symbols)} symbols available")
                
                # Test market data access with a common symbol
                test_symbol = 'EURUSD'
                if any(s.name == test_symbol for s in symbols):
                    tick = mt5.symbol_info_tick(test_symbol)
                    if tick:
                        state['market_data_accessible'] = True
                        logger.info(f"🔍 POST_SWITCH: Market data accessible - {test_symbol} bid: {tick.bid}")
            
            # Test order placement capability (dry run)
            if state['account_verified'] and state['trading_permissions'].get('trade_allowed'):
                # Test with order_check instead of actual order
                test_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": "EURUSD",
                    "volume": 0.01,
                    "type": mt5.ORDER_TYPE_BUY,
                    "price": 1.0000,  # Will be rejected but tests the system
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_IOC,
                }
                
                check_result = mt5.order_check(test_request)
                if check_result is not None:
                    state['order_placement_test'] = True
                    logger.info("🔍 POST_SWITCH: Order placement system accessible")
                else:
                    logger.warning("⚠️ POST_SWITCH: Order placement system not accessible")
            
        except Exception as e:
            logger.error(f"❌ POST_SWITCH: Exception: {e}")
            state['error'] = str(e)
        
        return state
    
    def _analyze_issues(self, diagnosis: Dict[str, Any]) -> List[str]:
        """Analyze diagnosis results for issues"""
        issues = []
        
        pre_state = diagnosis['pre_switch_state']
        switch_process = diagnosis['switch_process']
        post_state = diagnosis['post_switch_state']
        
        # Pre-switch issues
        if not pre_state.get('mt5_initialized'):
            issues.append("MT5 not properly initialized before switch")
        
        if not pre_state.get('terminal_connected'):
            issues.append("Terminal not connected before switch")
        
        # Switch process issues
        if not switch_process.get('login_successful'):
            issues.append(f"Login failed: {switch_process.get('login_error', 'Unknown error')}")
        
        if not switch_process.get('server_reachable'):
            issues.append("Server appears unreachable")
        
        if not switch_process.get('credentials_valid'):
            issues.append("Credentials appear invalid")
        
        if switch_process.get('retry_attempts', 0) > 1:
            issues.append(f"Required {switch_process['retry_attempts']} attempts to login")
        
        # Post-switch issues
        if not post_state.get('account_verified'):
            issues.append("Account not verified after switch")
        
        if not post_state.get('account_number_match'):
            issues.append("Account number mismatch after switch")
        
        if not post_state.get('trading_permissions', {}).get('trade_allowed'):
            issues.append("Trading not allowed on switched account")
        
        if not post_state.get('market_data_accessible'):
            issues.append("Market data not accessible after switch")
        
        return issues
    
    def _generate_recommendations(self, diagnosis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on diagnosis"""
        recommendations = []
        issues = diagnosis['issues_found']
        
        if any('MT5 not properly initialized' in issue for issue in issues):
            recommendations.append("Ensure MT5 is properly initialized before account switching")
        
        if any('Server appears unreachable' in issue for issue in issues):
            recommendations.append("Check network connectivity and server status")
        
        if any('Credentials appear invalid' in issue for issue in issues):
            recommendations.append("Verify account credentials and server settings")
        
        if any('Trading not allowed' in issue for issue in issues):
            recommendations.append("Check account trading permissions and auto-trading settings")
        
        if any('Market data not accessible' in issue for issue in issues):
            recommendations.append("Verify symbol subscriptions and market data permissions")
        
        if any('attempts to login' in issue for issue in issues):
            recommendations.append("Consider increasing retry delays or checking for connection stability")
        
        return recommendations
    
    def get_diagnostic_summary(self) -> Dict[str, Any]:
        """Get summary of all diagnostics"""
        return {
            'total_attempts': self.switch_attempts,
            'successful_switches': self.successful_switches,
            'success_rate': self.successful_switches / max(self.switch_attempts, 1) * 100,
            'last_successful_account': self.last_successful_account.account_number if self.last_successful_account else None,
            'recent_issues': [d['issues_found'] for d in self.diagnostic_history[-5:] if d['issues_found']],
            'common_recommendations': self._get_common_recommendations()
        }
    
    def _get_common_recommendations(self) -> List[str]:
        """Get most common recommendations from recent diagnostics"""
        all_recommendations = []
        for diagnosis in self.diagnostic_history[-10:]:  # Last 10 diagnostics
            all_recommendations.extend(diagnosis.get('recommendations', []))
        
        # Count frequency and return most common
        from collections import Counter
        common = Counter(all_recommendations).most_common(5)
        return [rec for rec, count in common if count > 1]


# Global diagnostics instance
account_switch_diagnostics = AccountSwitchDiagnostics()
