#!/usr/bin/env python3
"""
Simple Risk Management Compliance Test
Tests risk management without full system dependencies
"""

import sys
import os
import json
from datetime import datetime, timedelta

def test_daily_loss_limit_logic():
    """Test daily loss limit logic"""
    print("📉 TESTING DAILY LOSS LIMIT LOGIC")
    print("=" * 50)
    
    # Load account settings
    accounts_file = os.path.join('config', 'accounts.json')
    
    if not os.path.exists(accounts_file):
        print("❌ accounts.json not found")
        return False
    
    with open(accounts_file, 'r') as f:
        accounts_data = json.load(f)
    
    account = accounts_data['accounts'][0]  # demo1
    max_daily_loss = account['money_management_settings']['max_daily_loss']
    
    print(f"Account: {account['account_id']}")
    print(f"Max Daily Loss Limit: ${max_daily_loss}")
    
    # Test scenarios
    test_scenarios = [
        {"daily_pnl": 0.0, "should_allow": True, "description": "No loss"},
        {"daily_pnl": -1.5, "should_allow": True, "description": "Small loss"},
        {"daily_pnl": -2.9, "should_allow": True, "description": "Near limit"},
        {"daily_pnl": -3.0, "should_allow": False, "description": "At limit"},
        {"daily_pnl": -3.5, "should_allow": False, "description": "Over limit"},
        {"daily_pnl": -10.0, "should_allow": False, "description": "Far over limit"}
    ]
    
    all_passed = True
    
    for scenario in test_scenarios:
        # Simulate the risk check logic
        daily_loss_exceeded = scenario['daily_pnl'] <= -max_daily_loss
        can_trade = not daily_loss_exceeded
        
        print(f"\n  {scenario['description']}:")
        print(f"    Daily P&L: ${scenario['daily_pnl']:.1f}")
        print(f"    Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
        print(f"    Actual: {'ALLOW' if can_trade else 'BLOCK'}")
        
        is_correct = can_trade == scenario['should_allow']
        print(f"    Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
        
        if not is_correct:
            all_passed = False
    
    return all_passed

def test_risk_percent_validation():
    """Test risk percent calculations"""
    print("\n💰 TESTING RISK PERCENT VALIDATION")
    print("=" * 50)
    
    # Load account settings
    accounts_file = os.path.join('config', 'accounts.json')
    
    with open(accounts_file, 'r') as f:
        accounts_data = json.load(f)
    
    account = accounts_data['accounts'][0]
    risk_percent = account['money_management_settings']['risk_percent']
    max_risk_per_trade = account['money_management_settings']['max_risk_per_trade']
    
    # Simulate current balance
    balance = 74.40
    
    print(f"Account Balance: ${balance}")
    print(f"Risk Percent: {risk_percent}%")
    print(f"Max Risk Per Trade: {max_risk_per_trade}%")
    
    # Calculate risk amounts
    normal_risk = balance * (risk_percent / 100)
    max_risk = balance * (max_risk_per_trade / 100)
    
    print(f"Normal Risk Amount: ${normal_risk:.2f}")
    print(f"Max Risk Amount: ${max_risk:.2f}")
    
    # Test scenarios
    risk_scenarios = [
        {"risk_amount": 1.49, "should_allow": True, "description": "Normal risk (2%)"},
        {"risk_amount": 5.00, "should_allow": True, "description": "Medium risk (6.7%)"},
        {"risk_amount": 7.44, "should_allow": True, "description": "Max risk (10%)"},
        {"risk_amount": 8.00, "should_allow": False, "description": "Over max risk"},
        {"risk_amount": 15.00, "should_allow": False, "description": "Far over max risk"}
    ]
    
    all_passed = True
    
    for scenario in risk_scenarios:
        # Simulate risk validation logic
        risk_exceeded = scenario['risk_amount'] > max_risk
        can_trade = not risk_exceeded
        
        risk_percent_actual = (scenario['risk_amount'] / balance) * 100
        
        print(f"\n  {scenario['description']}:")
        print(f"    Risk Amount: ${scenario['risk_amount']:.2f} ({risk_percent_actual:.1f}%)")
        print(f"    Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
        print(f"    Actual: {'ALLOW' if can_trade else 'BLOCK'}")
        
        is_correct = can_trade == scenario['should_allow']
        print(f"    Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
        
        if not is_correct:
            all_passed = False
    
    return all_passed

def test_position_limits():
    """Test position and trade limits"""
    print("\n📊 TESTING POSITION LIMITS")
    print("=" * 50)
    
    # Load account settings
    accounts_file = os.path.join('config', 'accounts.json')
    
    with open(accounts_file, 'r') as f:
        accounts_data = json.load(f)
    
    account = accounts_data['accounts'][0]
    max_daily_trades = account['money_management_settings']['max_daily_trades']
    max_open_positions = account['money_management_settings']['max_open_positions']
    max_pending_orders = account['money_management_settings']['max_pending_orders']
    
    print(f"Max Daily Trades: {max_daily_trades}")
    print(f"Max Open Positions: {max_open_positions}")
    print(f"Max Pending Orders: {max_pending_orders}")
    
    # Test daily trades limit
    trade_scenarios = [
        {"daily_trades": 0, "should_allow": True, "description": "No trades today"},
        {"daily_trades": 3, "should_allow": True, "description": "Some trades"},
        {"daily_trades": 5, "should_allow": False, "description": "At limit"},
        {"daily_trades": 6, "should_allow": False, "description": "Over limit"}
    ]
    
    all_passed = True
    
    print("\n  Daily Trades Limit:")
    for scenario in trade_scenarios:
        trades_exceeded = scenario['daily_trades'] >= max_daily_trades
        can_trade = not trades_exceeded
        
        print(f"    {scenario['description']}: {scenario['daily_trades']} trades")
        print(f"      Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
        print(f"      Actual: {'ALLOW' if can_trade else 'BLOCK'}")
        
        is_correct = can_trade == scenario['should_allow']
        print(f"      Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
        
        if not is_correct:
            all_passed = False
    
    # Test position limits
    position_scenarios = [
        {"open_positions": 0, "should_allow": True, "description": "No positions"},
        {"open_positions": 1, "should_allow": True, "description": "One position"},
        {"open_positions": 2, "should_allow": False, "description": "At limit"},
        {"open_positions": 3, "should_allow": False, "description": "Over limit"}
    ]
    
    print("\n  Open Positions Limit:")
    for scenario in position_scenarios:
        positions_exceeded = scenario['open_positions'] >= max_open_positions
        can_trade = not positions_exceeded
        
        print(f"    {scenario['description']}: {scenario['open_positions']} positions")
        print(f"      Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
        print(f"      Actual: {'ALLOW' if can_trade else 'BLOCK'}")
        
        is_correct = can_trade == scenario['should_allow']
        print(f"      Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
        
        if not is_correct:
            all_passed = False
    
    return all_passed

def test_risk_multiplier_limits():
    """Test risk multiplier limits"""
    print("\n🔢 TESTING RISK MULTIPLIER LIMITS")
    print("=" * 50)
    
    # Load account settings
    accounts_file = os.path.join('config', 'accounts.json')
    
    with open(accounts_file, 'r') as f:
        accounts_data = json.load(f)
    
    account = accounts_data['accounts'][0]
    risk_percent = account['money_management_settings']['risk_percent']
    max_risk_multiplier = account['money_management_settings']['max_risk_multiplier']
    
    balance = 74.40
    base_risk = balance * (risk_percent / 100)
    max_allowed_risk = base_risk * max_risk_multiplier
    
    print(f"Base Risk (2%): ${base_risk:.2f}")
    print(f"Max Risk Multiplier: {max_risk_multiplier}x")
    print(f"Max Allowed Risk: ${max_allowed_risk:.2f}")
    
    # Test scenarios
    multiplier_scenarios = [
        {"risk_amount": 1.49, "multiplier": 1.0, "should_allow": True, "description": "1x base risk"},
        {"risk_amount": 7.44, "multiplier": 5.0, "should_allow": True, "description": "5x base risk"},
        {"risk_amount": 14.88, "multiplier": 10.0, "should_allow": True, "description": "10x base risk (at limit)"},
        {"risk_amount": 16.00, "multiplier": 10.7, "should_allow": False, "description": "Over 10x limit"},
        {"risk_amount": 30.00, "multiplier": 20.1, "should_allow": False, "description": "Far over limit"}
    ]
    
    all_passed = True
    
    for scenario in multiplier_scenarios:
        # Simulate risk multiplier validation
        multiplier_exceeded = scenario['risk_amount'] > max_allowed_risk
        can_trade = not multiplier_exceeded
        
        print(f"\n  {scenario['description']}:")
        print(f"    Risk Amount: ${scenario['risk_amount']:.2f}")
        print(f"    Multiplier: {scenario['multiplier']:.1f}x")
        print(f"    Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
        print(f"    Actual: {'ALLOW' if can_trade else 'BLOCK'}")
        
        is_correct = can_trade == scenario['should_allow']
        print(f"    Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
        
        if not is_correct:
            all_passed = False
    
    return all_passed

def main():
    """Main test function"""
    print("🛡️ RISK MANAGEMENT COMPLIANCE TEST")
    print("=" * 60)
    
    tests = [
        ("Daily Loss Limit", test_daily_loss_limit_logic),
        ("Risk Percent Validation", test_risk_percent_validation),
        ("Position Limits", test_position_limits),
        ("Risk Multiplier Limits", test_risk_multiplier_limits)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n🎯 TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = [r for r in results if r[1]]
    total_tests = len(results)
    
    for test_name, passed in results:
        print(f"{test_name}: {'✅ PASS' if passed else '❌ FAIL'}")
    
    print(f"\nOverall: {len(passed_tests)}/{total_tests} tests passed")
    
    overall_success = len(passed_tests) == total_tests
    print(f"Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
