#!/usr/bin/env python3
"""
Test Environment Variables Impact
Verify that .env variables actually control system behavior
"""

import sys
import os
import json
from pathlib import Path
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from signal_generation.signal_generator import SignalGenerator
from account_management.account_manager import AccountManager
from logging_system.logger import setup_logger

def test_env_loading():
    """Test that environment variables are loaded correctly"""
    print("🔍 TESTING ENVIRONMENT VARIABLE LOADING")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    # Test key variables
    env_vars = {
        'SIGNAL_GENERATION_INTERVAL': os.getenv('SIGNAL_GENERATION_INTERVAL', 'NOT_SET'),
        'TRADE_MANAGEMENT_INTERVAL': os.getenv('TRADE_MANAGEMENT_INTERVAL', 'NOT_SET'),
        'MAX_DAILY_TRADES_PER_ACCOUNT': os.getenv('MAX_DAILY_TRADES_PER_ACCOUNT', 'NOT_SET'),
        'MAX_OPEN_POSITIONS': os.getenv('MAX_OPEN_POSITIONS', 'NOT_SET'),
        'MAX_DAILY_LOSS_DOLLARS': os.getenv('MAX_DAILY_LOSS_DOLLARS', 'NOT_SET'),
        'MAX_DRAWDOWN_PERCENT': os.getenv('MAX_DRAWDOWN_PERCENT', 'NOT_SET'),
    }
    
    print("📊 Environment Variables:")
    for var, value in env_vars.items():
        status = "✅" if value != 'NOT_SET' else "❌"
        print(f"   {status} {var}: {value}")
    
    return all(value != 'NOT_SET' for value in env_vars.values())

def test_signal_generator_config():
    """Test that SignalGenerator uses environment variables"""
    print("\n🎯 TESTING SIGNAL GENERATOR CONFIGURATION")
    print("=" * 60)
    
    # Load environment
    load_dotenv()
    
    # Setup logger
    logger = setup_logger()
    
    # Load account manager
    account_manager = AccountManager()
    if not account_manager.load_accounts():
        print("❌ Failed to load accounts")
        return False
    
    # Create signal generator
    try:
        signal_generator = SignalGenerator(account_manager)
        
        print("📊 Signal Generator Configuration:")
        print(f"   📈 Max Daily Trades: {signal_generator.max_daily_trades}")
        print(f"   📈 Max Open Positions: {signal_generator.max_open_positions}")
        print(f"   📈 Max Daily Loss: ${signal_generator.max_daily_loss}")
        print(f"   📈 Max Drawdown: {signal_generator.max_drawdown_percent}%")
        print(f"   📈 Min Signal Interval: {signal_generator.min_signal_interval} minutes")
        
        # Verify values match environment
        expected_values = {
            'max_daily_trades': int(os.getenv('MAX_DAILY_TRADES_PER_ACCOUNT', '5')),
            'max_open_positions': int(os.getenv('MAX_OPEN_POSITIONS', '3')),
            'max_daily_loss': float(os.getenv('MAX_DAILY_LOSS_DOLLARS', '5.0')),
            'max_drawdown_percent': float(os.getenv('MAX_DRAWDOWN_PERCENT', '10.0')),
        }
        
        print("\n🔍 Verification:")
        all_correct = True
        for attr, expected in expected_values.items():
            actual = getattr(signal_generator, attr)
            if actual == expected:
                print(f"   ✅ {attr}: {actual} (matches env)")
            else:
                print(f"   ❌ {attr}: {actual} (expected {expected})")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Error creating signal generator: {e}")
        return False

def test_interval_changes():
    """Test that changing intervals in .env affects the system"""
    print("\n⏰ TESTING INTERVAL CONFIGURATION")
    print("=" * 60)
    
    # Load current values
    load_dotenv()
    
    signal_interval = int(os.getenv('SIGNAL_GENERATION_INTERVAL', '300'))
    trade_interval = int(os.getenv('TRADE_MANAGEMENT_INTERVAL', '60'))
    
    print("📊 Current Intervals:")
    print(f"   🔄 Signal Generation: {signal_interval} seconds ({signal_interval/60:.1f} minutes)")
    print(f"   🔄 Trade Management: {trade_interval} seconds ({trade_interval/60:.1f} minutes)")
    
    # Calculate cost impact
    daily_signals = (24 * 60 * 60) / signal_interval
    monthly_signals = daily_signals * 30
    
    # Estimate cost (assuming 2500 tokens per prompt)
    cost_per_1k_tokens = 0.002  # Approximate
    tokens_per_prompt = 2500
    monthly_cost = (monthly_signals * tokens_per_prompt / 1000) * cost_per_1k_tokens
    
    print(f"\n💰 Cost Impact:")
    print(f"   📊 Daily Signals: ~{daily_signals:.0f}")
    print(f"   📊 Monthly Signals: ~{monthly_signals:.0f}")
    print(f"   📊 Estimated Monthly Cost: ~${monthly_cost:.2f}")
    
    # Recommendations
    if signal_interval < 600:  # Less than 10 minutes
        print(f"   ⚠️  WARNING: Short interval may be expensive!")
    elif signal_interval > 1800:  # More than 30 minutes
        print(f"   ⚠️  WARNING: Long interval may miss opportunities!")
    else:
        print(f"   ✅ Interval looks reasonable for cost/performance balance")
    
    return True

def test_risk_management_integration():
    """Test that risk management settings are properly integrated"""
    print("\n🛡️ TESTING RISK MANAGEMENT INTEGRATION")
    print("=" * 60)
    
    load_dotenv()
    
    # Test account configuration
    try:
        with open('config/accounts.json', 'r') as f:
            accounts_config = json.load(f)
        
        account = accounts_config['accounts'][0]
        
        print("📊 Account Configuration:")
        print(f"   💰 Account: {account['account_number']}")
        print(f"   💰 Strategy: {account['strategy']}")
        print(f"   💰 Money Management: {account['money_management']}")
        
        # Check money management settings
        mm_settings = account.get('money_management_settings', {})
        print(f"\n📊 Money Management Settings:")
        for key, value in mm_settings.items():
            print(f"   📈 {key}: {value}")
        
        # Environment risk settings
        print(f"\n📊 Environment Risk Settings:")
        print(f"   🛡️ Max Daily Trades: {os.getenv('MAX_DAILY_TRADES_PER_ACCOUNT', 'NOT_SET')}")
        print(f"   🛡️ Max Open Positions: {os.getenv('MAX_OPEN_POSITIONS', 'NOT_SET')}")
        print(f"   🛡️ Max Daily Loss: ${os.getenv('MAX_DAILY_LOSS_DOLLARS', 'NOT_SET')}")
        print(f"   🛡️ Max Drawdown: {os.getenv('MAX_DRAWDOWN_PERCENT', 'NOT_SET')}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing risk management: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING ENVIRONMENT VARIABLE INTEGRATION")
    print("Testing that .env changes actually affect system behavior")
    print("=" * 80)
    
    tests = [
        ("Environment Loading", test_env_loading),
        ("Signal Generator Config", test_signal_generator_config),
        ("Interval Configuration", test_interval_changes),
        ("Risk Management Integration", test_risk_management_integration),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("📊 TEST SUMMARY")
    print("="*80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - Environment variables are properly integrated!")
        print("\n📝 Key Points:")
        print("   ✅ .env variables are loaded and used by the system")
        print("   ✅ Signal generation intervals are configurable")
        print("   ✅ Risk management limits are enforced")
        print("   ✅ Cost optimization settings are active")
    else:
        print("⚠️  Some tests failed - check environment configuration")
    
    print("\n💡 To modify system behavior:")
    print("   1. Edit the .env file")
    print("   2. Restart the trading system")
    print("   3. Changes will take effect immediately")

if __name__ == "__main__":
    main()
