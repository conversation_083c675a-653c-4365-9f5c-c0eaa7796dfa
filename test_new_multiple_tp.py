#!/usr/bin/env python3
"""
Test the new multiple TP risk management implementation
"""

import sys
sys.path.append('src')

from money_management.percent_risk import PercentRiskStrategy
from money_management.base_strategy import AccountInfo

def test_new_multiple_tp_logic():
    """Test the new multiple TP risk calculation logic"""
    print("=" * 60)
    print("TESTING NEW MULTIPLE TP RISK MANAGEMENT")
    print("=" * 60)
    
    # Current account setup
    balance = 74.40
    account_info = AccountInfo(
        balance=balance,
        equity=balance,
        margin=0,
        free_margin=balance/2,
        margin_level=1000,
        currency="USD",
        leverage=500
    )
    
    # Updated config (2% risk instead of 0.5%)
    config = {"risk_percent": 2.0}
    strategy = PercentRiskStrategy(config)
    
    # Market data
    market_data = {
        "pip_value": 10.0,
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    # Simulate a signal similar to what was generated
    signal = {
        "entry_price": 1.15392,
        "stop_loss": 1.15542,
        "take_profit_levels": [
            {"price": 1.15242, "volume_percent": 50},
            {"price": 1.15092, "volume_percent": 30},
            {"price": 1.14942, "volume_percent": 20}
        ]
    }
    
    print(f"Account Balance: ${balance}")
    print(f"Risk Percent: {config['risk_percent']}%")
    print(f"Expected Risk: ${balance * (config['risk_percent']/100):.2f}")
    print(f"Entry: {signal['entry_price']}")
    print(f"Stop Loss: {signal['stop_loss']}")
    
    # Calculate stop loss distance
    pip_difference = abs(signal['entry_price'] - signal['stop_loss']) / market_data['pip_size']
    print(f"Stop Loss Distance: {pip_difference:.0f} pips")
    print()
    
    # Calculate base volume using money management
    trade_params = strategy.calculate_position_size(
        account_info=account_info,
        symbol="EURUSD",
        entry_price=signal['entry_price'],
        stop_loss=signal['stop_loss'],
        trade_history=[],
        market_data=market_data
    )
    
    print("MONEY MANAGEMENT CALCULATION:")
    print(f"  Base Volume: {trade_params.volume:.2f} lots")
    print(f"  Base Risk: ${trade_params.risk_amount:.2f}")
    print()
    
    # NEW LOGIC: Calculate each TP level with proper risk management
    print("NEW MULTIPLE TP LOGIC:")
    total_volume = 0
    total_risk = 0
    
    for i, tp_level in enumerate(signal['take_profit_levels'], 1):
        volume_percent = tp_level['volume_percent']
        
        # NEW: Calculate volume for this specific TP level using money management
        tp_trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=signal['entry_price'],
            stop_loss=signal['stop_loss'],
            trade_history=[],
            market_data=market_data
        )
        
        # Apply volume percentage to the calculated volume
        tp_volume = round(tp_trade_params.volume * (volume_percent / 100), 2)
        
        # Ensure minimum volume
        if tp_volume < market_data['min_volume']:
            tp_volume = market_data['min_volume']
        
        # Calculate risk for this TP level
        tp_risk = pip_difference * market_data['pip_value'] * tp_volume
        risk_percent_actual = (tp_risk / balance) * 100
        
        total_volume += tp_volume
        total_risk += tp_risk
        
        print(f"  TP{i} ({volume_percent}%):")
        print(f"    Price: {tp_level['price']}")
        print(f"    Calculated Volume: {tp_trade_params.volume * (volume_percent / 100):.4f} lots")
        print(f"    Final Volume: {tp_volume:.2f} lots")
        print(f"    Risk: ${tp_risk:.2f} ({risk_percent_actual:.2f}% of balance)")
        print()
    
    # Summary
    total_risk_percent = (total_risk / balance) * 100
    expected_risk = balance * (config['risk_percent'] / 100)
    risk_multiplier = total_risk / expected_risk
    
    print("SUMMARY:")
    print(f"  Total Volume: {total_volume:.2f} lots")
    print(f"  Total Risk: ${total_risk:.2f}")
    print(f"  Total Risk %: {total_risk_percent:.2f}%")
    print(f"  Expected Risk: ${expected_risk:.2f}")
    print(f"  Risk Multiplier: {risk_multiplier:.1f}x")
    print()
    
    # Check threshold setting
    threshold = 200.0  # From config
    print("THRESHOLD CHECK:")
    print(f"  Account Balance: ${balance}")
    print(f"  Threshold: ${threshold}")
    print(f"  Multiple TP Enabled: {'YES' if balance >= threshold else 'NO'}")
    
    if balance < threshold:
        print("  ⚠️  Multiple TP would be DISABLED - using single TP instead")
        print(f"  Single TP Risk: ${pip_difference * market_data['pip_value'] * trade_params.volume:.2f}")
    else:
        print("  ✅ Multiple TP enabled")

def test_threshold_behavior():
    """Test the threshold behavior for disabling multiple TP"""
    print("\n" + "=" * 60)
    print("TESTING THRESHOLD BEHAVIOR")
    print("=" * 60)
    
    # Test different account balances
    test_balances = [50, 100, 150, 200, 250, 500]
    threshold = 200.0
    
    for balance in test_balances:
        print(f"\nBalance: ${balance}")
        print(f"  Multiple TP: {'ENABLED' if balance >= threshold else 'DISABLED'}")
        
        if balance < threshold:
            print(f"  → Would use single TP to avoid excessive risk")
        else:
            print(f"  → Would use multiple TP levels")

if __name__ == "__main__":
    test_new_multiple_tp_logic()
    test_threshold_behavior()
