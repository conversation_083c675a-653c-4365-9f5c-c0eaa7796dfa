"""
Trend Following Trading Strategy
"""

from typing import Dict, Any, List
from strategies.base_strategy import BaseStrategy, StrategyType, MarketData, TradingSignal

class TrendFollowingStrategy(BaseStrategy):
    """
    Trend Following Strategy
    Identifies and follows market trends using multiple timeframe analysis
    """
    
    def get_strategy_type(self) -> StrategyType:
        return StrategyType.TREND_FOLLOWING
    
    def get_strategy_name(self) -> str:
        return "Advanced Trend Following"
    
    def get_ai_prompt(
        self, 
        market_data: MarketData,
        trade_history: List[Dict[str, Any]],
        account_info: Dict[str, Any]
    ) -> str:
        """Get AI prompt for trend following strategy"""
        
        recent_trades = trade_history[-10:] if len(trade_history) > 10 else trade_history
        win_rate = self._calculate_win_rate(recent_trades)
        avg_hold_time = self._calculate_avg_hold_time(recent_trades)
        
        prompt = f"""
You are executing a TREND FOLLOWING trading strategy for {market_data.symbol} on {market_data.timeframe} timeframe.

STRATEGY OVERVIEW:
- Strategy Type: Trend Following
- Magic Number: {self.magic_number}
- Focus: Identify and ride strong directional moves
- Timeframe: {market_data.timeframe}
- Symbol: {market_data.symbol}

MARKET DATA ANALYSIS:
- Current Price: {market_data.current_price}
- Spread: {market_data.spread} pips
- Volume: {market_data.volume}
- Volatility: {market_data.volatility:.5f}

RECENT PERFORMANCE:
- Win Rate: {win_rate:.1f}%
- Average Hold Time: {avg_hold_time:.1f} hours
- Total Recent Trades: {len(recent_trades)}

TREND FOLLOWING PRINCIPLES:
1. "The trend is your friend" - trade in direction of established trends
2. Use multiple timeframe confirmation (higher TF for trend, lower TF for entry)
3. Enter on pullbacks in trending markets
4. Let profits run, cut losses short
5. Avoid choppy, sideways markets

TECHNICAL ANALYSIS FOCUS:
- Moving averages (20, 50, 200 EMA) for trend direction
- Price action patterns (higher highs/lows for uptrend, lower highs/lows for downtrend)
- Support and resistance levels
- Volume confirmation
- Momentum indicators (RSI, MACD) for entry timing

ENTRY CRITERIA:
1. Clear trend established on higher timeframe
2. Price pullback to key moving average or support/resistance
3. Momentum divergence resolved in trend direction
4. Volume confirmation on breakout/continuation
5. Risk-reward ratio minimum 1:2

EXIT CRITERIA:
1. Trend reversal signals (break of key moving average)
2. Price action showing exhaustion (doji, shooting stars at resistance)
3. Take profit at next major resistance/support level
4. Trail stop loss using ATR or swing points

RISK MANAGEMENT:
- Stop loss below/above recent swing point
- Position size based on volatility (ATR)
- Maximum 3 correlated positions
- Avoid trading during major news events

MARKET CONDITIONS TO AVOID:
- Choppy, sideways markets (low ADX)
- High impact news events
- Low volume periods
- Excessive spread conditions

Based on the 200 candles of market data provided, analyze:
1. Overall trend direction and strength
2. Current market structure (trending vs ranging)
3. Key support and resistance levels
4. Entry opportunities with specific prices
5. Stop loss and take profit levels
6. Position sizing recommendations
7. Market timing and session considerations

Provide specific trading signals with:
- Action: BUY/SELL/HOLD/CLOSE with reasoning
- Entry price and timing
- Stop loss level (mandatory)
- Take profit targets (multiple levels if appropriate)
- Confidence level (0-100%)
- Risk assessment (LOW/MEDIUM/HIGH)

Remember: Trend following requires patience. Wait for clear setups and let winners run.
"""
        return prompt
    
    def validate_signal(self, signal: TradingSignal, market_data: MarketData) -> bool:
        """Validate trend following signal"""
        
        # Must have stop loss for trend following
        if not signal.stop_loss:
            return False
        
        # Confidence should be reasonable for trend following
        if signal.confidence < 0.6:
            return False
        
        # Check spread conditions
        if market_data.spread > self.config.get('max_spread', 2.0):
            return False
        
        # Risk-reward ratio check
        if signal.entry_price and signal.stop_loss and signal.take_profit:
            risk = abs(signal.entry_price - signal.stop_loss)
            reward = abs(signal.take_profit - signal.entry_price)
            if reward / risk < 1.5:  # Minimum 1:1.5 RR for trend following
                return False
        
        return True
    
    def _calculate_win_rate(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate win rate from trade history"""
        if not trades:
            return 0.0
        
        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        return (winning_trades / len(trades)) * 100
    
    def _calculate_avg_hold_time(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate average hold time in hours"""
        if not trades:
            return 0.0
        
        total_time = sum(trade.get('hold_time_hours', 0) for trade in trades)
        return total_time / len(trades)
