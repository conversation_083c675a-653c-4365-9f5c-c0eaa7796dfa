#!/usr/bin/env python3
"""
Test script to verify price normalization fixes for MT5 integration
This script tests the new price normalization functionality to prevent "Invalid price" errors
"""

import os
import sys
from pathlib import Path

# Add src directory to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from mt5_integration.mt5_client import MT5Client
from account_management.account_manager import AccountManager
from validation.trade_validator import TradeValidator
import MetaTrader5 as mt5

def test_price_normalization():
    """Test price normalization functionality"""
    print("="*60)
    print("🔍 TESTING PRICE NORMALIZATION FIXES")
    print("="*60)
    
    # Initialize MT5 client
    client = MT5Client()
    
    # Test price normalization methods
    test_symbols = ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"]
    test_prices = [1.*********, 1.*********, 150.123456, 2500.987654]
    
    print("\n📊 Testing _normalize_price method:")
    for symbol, price in zip(test_symbols, test_prices):
        try:
            normalized = client._normalize_price(symbol, price)
            print(f"   {symbol}: {price} → {normalized}")
        except Exception as e:
            print(f"   ❌ {symbol}: Error - {e}")
    
    print("\n📊 Testing _validate_price_for_symbol method:")
    for symbol, price in zip(test_symbols, test_prices):
        try:
            validated = client._validate_price_for_symbol(symbol, price, "BUY")
            print(f"   {symbol}: {price} → {validated}")
        except Exception as e:
            print(f"   ❌ {symbol}: Error - {e}")

def test_trade_validator_normalization():
    """Test trade validator price normalization"""
    print("\n" + "="*60)
    print("🔍 TESTING TRADE VALIDATOR NORMALIZATION")
    print("="*60)
    
    validator = TradeValidator()
    
    test_symbols = ["EURUSD", "GBPUSD", "USDJPY"]
    test_prices = [1.*********, 1.*********, 150.123456]
    
    print("\n📊 Testing validator _normalize_price method:")
    for symbol, price in zip(test_symbols, test_prices):
        try:
            normalized = validator._normalize_price(symbol, price)
            print(f"   {symbol}: {price} → {normalized}")
        except Exception as e:
            print(f"   ❌ {symbol}: Error - {e}")

def test_symbol_digits():
    """Test symbol digits retrieval"""
    print("\n" + "="*60)
    print("🔍 TESTING SYMBOL DIGITS INFORMATION")
    print("="*60)
    
    test_symbols = ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD", "BTCUSD"]
    
    for symbol in test_symbols:
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info:
                print(f"   {symbol}:")
                print(f"      Digits: {symbol_info.digits}")
                print(f"      Point: {symbol_info.point}")
                print(f"      Trade stops level: {symbol_info.trade_stops_level}")
                
                # Test price normalization
                test_price = 1.*********
                normalized = round(test_price, symbol_info.digits)
                print(f"      Test price {test_price} → {normalized}")
            else:
                print(f"   ❌ {symbol}: Symbol info not available")
        except Exception as e:
            print(f"   ❌ {symbol}: Error - {e}")

def test_order_modification_scenario():
    """Test a realistic order modification scenario"""
    print("\n" + "="*60)
    print("🔍 TESTING ORDER MODIFICATION SCENARIO")
    print("="*60)
    
    # Initialize components
    account_manager = AccountManager()
    
    try:
        # Load accounts
        accounts = account_manager.load_accounts()
        if not accounts:
            print("❌ No accounts found. Please configure accounts first.")
            return
        
        # Login to first account
        account = accounts[0]
        client = MT5Client()
        
        if not client.login(account):
            print(f"❌ Failed to login to account {account.account_number}")
            return
        
        print(f"✅ Logged in to account {account.account_number}")
        
        # Get pending orders
        orders = mt5.orders_get()
        if orders:
            print(f"\n📊 Found {len(orders)} pending orders")
            
            # Test modification on first order (without actually modifying)
            order = orders[0]
            print(f"\n🔍 Testing modification for order {order.ticket}:")
            print(f"   Symbol: {order.symbol}")
            print(f"   Current price: {order.price_open}")
            print(f"   Current SL: {order.sl}")
            print(f"   Current TP: {order.tp}")
            
            # Test price normalization for this symbol
            test_price = order.price_open + 0.*********
            normalized_price = client._normalize_price(order.symbol, test_price)
            print(f"   Test price normalization: {test_price} → {normalized_price}")
            
            # Test validation
            validated_price = client._validate_price_for_symbol(order.symbol, test_price, "BUY_LIMIT")
            print(f"   Test price validation: {test_price} → {validated_price}")
            
        else:
            print("📊 No pending orders found")
            
        # Test with positions
        positions = mt5.positions_get()
        if positions:
            print(f"\n📊 Found {len(positions)} open positions")
            
            # Test modification on first position (without actually modifying)
            pos = positions[0]
            print(f"\n🔍 Testing modification for position {pos.ticket}:")
            print(f"   Symbol: {pos.symbol}")
            print(f"   Entry price: {pos.price_open}")
            print(f"   Current SL: {pos.sl}")
            print(f"   Current TP: {pos.tp}")
            
            # Test price normalization for this symbol
            if pos.sl > 0:
                test_sl = pos.sl + 0.*********
                normalized_sl = client._normalize_price(pos.symbol, test_sl)
                print(f"   Test SL normalization: {test_sl} → {normalized_sl}")
            
        else:
            print("📊 No open positions found")
            
    except Exception as e:
        print(f"❌ Error in order modification test: {e}")

def main():
    """Main test function"""
    print("🚀 Starting Price Normalization Fix Tests")
    
    try:
        # Initialize MT5 first
        if not mt5.initialize():
            print("❌ Failed to initialize MT5")
            return
        
        print("✅ MT5 initialized successfully")
        
        # Run tests
        test_symbol_digits()
        test_price_normalization()
        test_trade_validator_normalization()
        test_order_modification_scenario()
        
        print("\n" + "="*60)
        print("✅ PRICE NORMALIZATION TESTS COMPLETED")
        print("="*60)
        print("\nKey improvements implemented:")
        print("1. ✅ Added _normalize_price() method to MT5Client")
        print("2. ✅ Added _validate_price_for_symbol() method to MT5Client")
        print("3. ✅ Updated modify_order() to use price normalization")
        print("4. ✅ Updated modify_position() to use price normalization")
        print("5. ✅ Updated place_order() to use price normalization")
        print("6. ✅ Added price normalization to TradeValidator")
        print("7. ✅ Enhanced error handling for error code 10015")
        print("\nThese fixes should resolve the 'Invalid price' error (10015)")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        mt5.shutdown()

if __name__ == "__main__":
    main()
