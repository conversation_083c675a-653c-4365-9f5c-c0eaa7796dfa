"""
Base Money Management Strategy Class
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

class MoneyManagementType(Enum):
    FIXED_VOLUME = "fixed_volume"
    PERCENT_RISK = "percent_risk"
    MARTINGALE = "martingale"
    ANTI_MARTINGALE = "anti_martingale"

@dataclass
class TradeParameters:
    """Trade parameters calculated by money management strategy"""
    volume: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    risk_amount: float
    max_loss: float
    confidence_level: float

@dataclass
class AccountInfo:
    """Account information for money management calculations"""
    balance: float
    equity: float
    margin: float
    free_margin: float
    margin_level: float
    currency: str
    leverage: int

class BaseMoneyManagement(ABC):
    """Base class for all money management strategies"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.strategy_type = self.get_strategy_type()
    
    @abstractmethod
    def get_strategy_type(self) -> MoneyManagementType:
        """Return the strategy type"""
        pass
    
    @abstractmethod
    def calculate_position_size(
        self, 
        account_info: AccountInfo,
        symbol: str,
        entry_price: float,
        stop_loss: Optional[float],
        trade_history: list,
        market_data: Dict[str, Any]
    ) -> TradeParameters:
        """Calculate position size and trade parameters"""
        pass
    
    @abstractmethod
    def get_ai_prompt(self, account_info: AccountInfo, trade_history: list) -> str:
        """Get AI prompt specific to this money management strategy"""
        pass
    
    def validate_trade_parameters(self, params: TradeParameters, account_info: AccountInfo) -> bool:
        """Validate calculated trade parameters"""
        # Check if volume is within acceptable range
        if params.volume <= 0:
            return False
        
        # Check if risk amount doesn't exceed account balance
        if params.risk_amount > account_info.balance * 0.5:  # Max 50% risk
            return False
        
        # Check margin requirements
        required_margin = params.volume * 1000  # Simplified calculation
        if required_margin > account_info.free_margin:
            return False
        
        return True
