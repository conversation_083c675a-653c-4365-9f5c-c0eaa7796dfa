{"summary": {"total_tests": 27, "passed": 19, "failed": 8, "errors": 0, "skipped": 0, "success_rate": 70.**************, "total_duration": 4.535669, "start_time": "2025-08-01T14:07:58.502934", "end_time": "2025-08-01T14:08:03.038603"}, "status": "NOT_READY", "test_details": [{"test_name": "Account Management Tests_test_0", "status": "PASS", "duration": 0.*****************, "error_msg": null, "timestamp": "2025-08-01T14:07:58.535185"}, {"test_name": "Account Management Tests_test_1", "status": "PASS", "duration": 0.*****************, "error_msg": null, "timestamp": "2025-08-01T14:07:58.535185"}, {"test_name": "Account Management Tests_test_2", "status": "PASS", "duration": 0.*****************, "error_msg": null, "timestamp": "2025-08-01T14:07:58.535185"}, {"test_name": "Account Management Tests_test_3", "status": "PASS", "duration": 0.*****************, "error_msg": null, "timestamp": "2025-08-01T14:07:58.535185"}, {"test_name": "Money Management Tests_test_0", "status": "PASS", "duration": 0.0010531425476074218, "error_msg": null, "timestamp": "2025-08-01T14:07:58.542467"}, {"test_name": "Money Management Tests_test_1", "status": "PASS", "duration": 0.0010531425476074218, "error_msg": null, "timestamp": "2025-08-01T14:07:58.542467"}, {"test_name": "Money Management Tests_test_2", "status": "PASS", "duration": 0.0010531425476074218, "error_msg": null, "timestamp": "2025-08-01T14:07:58.542467"}, {"test_name": "Money Management Tests_test_3", "status": "PASS", "duration": 0.0010531425476074218, "error_msg": null, "timestamp": "2025-08-01T14:07:58.542467"}, {"test_name": "Money Management Tests_test_4", "status": "PASS", "duration": 0.0010531425476074218, "error_msg": null, "timestamp": "2025-08-01T14:07:58.542467"}, {"test_name": "Trading Strategy Tests_test_0", "status": "PASS", "duration": 0.0006678899129231771, "error_msg": null, "timestamp": "2025-08-01T14:07:58.545985"}, {"test_name": "Trading Strategy Tests_test_1", "status": "PASS", "duration": 0.0006678899129231771, "error_msg": null, "timestamp": "2025-08-01T14:07:58.545985"}, {"test_name": "Trading Strategy Tests_test_2", "status": "PASS", "duration": 0.0006678899129231771, "error_msg": null, "timestamp": "2025-08-01T14:07:58.545985"}, {"test_name": "AI Integration Tests_test_0", "status": "PASS", "duration": 0.004021453857421875, "error_msg": null, "timestamp": "2025-08-01T14:07:58.567365"}, {"test_name": "AI Integration Tests_test_1", "status": "PASS", "duration": 0.004021453857421875, "error_msg": null, "timestamp": "2025-08-01T14:07:58.567365"}, {"test_name": "AI Integration Tests_test_2", "status": "PASS", "duration": 0.004021453857421875, "error_msg": null, "timestamp": "2025-08-01T14:07:58.567365"}, {"test_name": "AI Integration Tests_test_3", "status": "PASS", "duration": 0.004021453857421875, "error_msg": null, "timestamp": "2025-08-01T14:07:58.567365"}, {"test_name": "AI Integration Tests_test_4", "status": "PASS", "duration": 0.004021453857421875, "error_msg": null, "timestamp": "2025-08-01T14:07:58.567365"}, {"test_name": "test_market_data_retrieval (test_comprehensive_trading_system.TestMT5Integration)", "status": "FAIL", "duration": 0.006102323532104492, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 589, in test_market_data_retrieval\n    self.assertIsNotNone(market_data)\nAssertionError: unexpectedly None\n", "timestamp": "2025-08-01T14:07:58.587357"}, {"test_name": "test_mt5_login_comprehensive (test_comprehensive_trading_system.TestMT5Integration)", "status": "FAIL", "duration": 0.006102323532104492, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 566, in test_mt5_login_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "timestamp": "2025-08-01T14:07:58.588356"}, {"test_name": "MT5 Integration Tests_test_0", "status": "PASS", "duration": 0.006102323532104492, "error_msg": null, "timestamp": "2025-08-01T14:07:58.588356"}, {"test_name": "test_closed_trade_retrieval (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.6356064251491002, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 302, in test_closed_trade_retrieval\n    self.assertEqual(len(trade_history), 3)\nAssertionError: 0 != 3\n", "timestamp": "2025-08-01T14:08:03.037601"}, {"test_name": "test_complete_signal_lifecycle (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.6356064251491002, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1845, in _inner\n    return await f(*args, **kw)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 146, in test_complete_signal_lifecycle\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 898, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n", "timestamp": "2025-08-01T14:08:03.037601"}, {"test_name": "test_magic_number_tracking (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.6356064251491002, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 83, in _callMaybeAsync\n    ret = func(*args, **kwargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 405, in test_magic_number_tracking\n    self.assertEqual(len(magic_numbers), len(strategy_types))\nAssertionError: 1 != 3\n", "timestamp": "2025-08-01T14:08:03.037601"}, {"test_name": "test_multiple_take_profit_execution (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.6356064251491002, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 199, in test_multiple_take_profit_execution\n    self.assertGreaterEqual(mock_mt5.order_send.call_count, 3)\nAssertionError: 0 not greater than or equal to 3\n", "timestamp": "2025-08-01T14:08:03.037601"}, {"test_name": "test_pending_order_management (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.6356064251491002, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 241, in test_pending_order_management\n    self.assertEqual(len(pending_orders), 2)\nAssertionError: 0 != 2\n", "timestamp": "2025-08-01T14:08:03.037601"}, {"test_name": "test_trade_lifecycle_with_ai_management (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.6356064251491002, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 389, in test_trade_lifecycle_with_ai_management\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 898, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n", "timestamp": "2025-08-01T14:08:03.037601"}, {"test_name": "Real-World Signal Execution Tests_test_0", "status": "PASS", "duration": 0.6356064251491002, "error_msg": null, "timestamp": "2025-08-01T14:08:03.037601"}], "failures": ["test_market_data_retrieval (test_comprehensive_trading_system.TestMT5Integration): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 589, in test_market_data_retrieval\n    self.assertIsNotNone(market_data)\nAssertionError: unexpectedly None\n", "test_mt5_login_comprehensive (test_comprehensive_trading_system.TestMT5Integration): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 566, in test_mt5_login_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "test_closed_trade_retrieval (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 302, in test_closed_trade_retrieval\n    self.assertEqual(len(trade_history), 3)\nAssertionError: 0 != 3\n", "test_complete_signal_lifecycle (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1845, in _inner\n    return await f(*args, **kw)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 146, in test_complete_signal_lifecycle\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 898, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n", "test_magic_number_tracking (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 83, in _callMaybeAsync\n    ret = func(*args, **kwargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 405, in test_magic_number_tracking\n    self.assertEqual(len(magic_numbers), len(strategy_types))\nAssertionError: 1 != 3\n", "test_multiple_take_profit_execution (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 199, in test_multiple_take_profit_execution\n    self.assertGreaterEqual(mock_mt5.order_send.call_count, 3)\nAssertionError: 0 not greater than or equal to 3\n", "test_pending_order_management (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 241, in test_pending_order_management\n    self.assertEqual(len(pending_orders), 2)\nAssertionError: 0 != 2\n", "test_trade_lifecycle_with_ai_management (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 389, in test_trade_lifecycle_with_ai_management\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 898, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n"], "errors": [], "recommendations": ["🚨 DO NOT deploy to production with current issues", "🚨 Fix all critical errors before proceeding", "🚨 Re-run full test suite after fixes", "📝 Consider code review for failing components", "📝 Test on demo accounts extensively before retry", "📊 Current success rate: 70.4% - Target: 100%"]}