#!/usr/bin/env python3
"""
End-to-End Integration Test
Tests the complete trading system with MT5 connection and real account validation
"""

import sys
import os
import asyncio
import json
from datetime import datetime, timedelta

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from mt5_integration.mt5_client import MT5Client
    from account_management.account_manager import AccountManager
    from money_management.percent_risk import PercentRiskStrategy
    from money_management.base_strategy import AccountInfo
    MT5_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  MT5 integration not available: {e}")
    MT5_AVAILABLE = False

class EndToEndValidator:
    """End-to-end system validation"""
    
    def __init__(self):
        self.test_results = []
        self.mt5_client = None
        
    async def run_comprehensive_validation(self):
        """Run comprehensive end-to-end validation"""
        print("🔄 END-TO-END INTEGRATION VALIDATION")
        print("=" * 60)
        
        # Test system components
        await self._test_account_loading()
        await self._test_mt5_connection()
        await self._test_money_management_integration()
        await self._test_risk_management_enforcement()
        await self._test_system_safety_checks()
        
        # Analyze results
        self._analyze_results()
        
    async def _test_account_loading(self):
        """Test account configuration loading"""
        print("\n📊 Testing Account Configuration Loading")
        print("-" * 40)
        
        try:
            account_manager = AccountManager()
            loaded = account_manager.load_accounts()
            
            if loaded:
                print(f"✅ Loaded {len(account_manager.accounts)} accounts")
                
                for account_id, account in account_manager.accounts.items():
                    print(f"  Account: {account_id}")
                    print(f"    Strategy: {account.strategy_type}")
                    print(f"    Money Management: {account.money_management_type}")
                    print(f"    Symbols: {len(account.symbols)}")
                    
                    # Validate required settings
                    required_settings = ['risk_percent', 'max_daily_loss', 'max_daily_trades']
                    missing = [s for s in required_settings if s not in account.money_management_config]
                    
                    if missing:
                        print(f"    ❌ Missing settings: {missing}")
                        self.test_results.append({
                            'test': 'account_loading',
                            'account': account_id,
                            'passed': False,
                            'error': f"Missing settings: {missing}"
                        })
                    else:
                        print(f"    ✅ All required settings present")
                        self.test_results.append({
                            'test': 'account_loading',
                            'account': account_id,
                            'passed': True
                        })
            else:
                print("❌ Failed to load accounts")
                self.test_results.append({
                    'test': 'account_loading',
                    'passed': False,
                    'error': 'Failed to load accounts'
                })
                
        except Exception as e:
            print(f"❌ Account loading error: {e}")
            self.test_results.append({
                'test': 'account_loading',
                'passed': False,
                'error': str(e)
            })
            
    async def _test_mt5_connection(self):
        """Test MT5 connection and basic operations"""
        print("\n🔌 Testing MT5 Connection")
        print("-" * 40)
        
        if not MT5_AVAILABLE:
            print("⚠️  MT5 not available, skipping connection test")
            self.test_results.append({
                'test': 'mt5_connection',
                'passed': False,
                'error': 'MT5 not available'
            })
            return
        
        try:
            self.mt5_client = MT5Client()
            
            # Test initialization
            if self.mt5_client.initialize():
                print("✅ MT5 initialized successfully")
                
                # Test account login
                accounts_file = os.path.join('config', 'accounts.json')
                if os.path.exists(accounts_file):
                    with open(accounts_file, 'r') as f:
                        accounts_data = json.load(f)
                    
                    account_config = accounts_data['accounts'][0]  # demo1
                    
                    # Create account object for login
                    from account_management.models import TradingAccount
                    account = TradingAccount(
                        account_id=account_config['account_id'],
                        account_number=account_config['account_number'],
                        server=account_config['server'],
                        username=str(account_config['account_number']),
                        password=account_config['password'],
                        strategy_type=account_config['strategy'],
                        money_management_type=account_config['money_management'],
                        symbols=[s['symbol'] for s in account_config['symbols']],
                        timeframes=[s['timeframe'] for s in account_config['symbols']]
                    )
                    
                    if self.mt5_client.login(account):
                        print("✅ MT5 login successful")
                        
                        # Test basic operations
                        account_info = self.mt5_client.get_account_info()
                        if account_info:
                            print(f"  Balance: ${account_info.balance:.2f}")
                            print(f"  Equity: ${account_info.equity:.2f}")
                            print(f"  Leverage: 1:{account_info.leverage}")
                            
                            self.test_results.append({
                                'test': 'mt5_connection',
                                'passed': True,
                                'balance': account_info.balance,
                                'equity': account_info.equity
                            })
                        else:
                            print("❌ Failed to get account info")
                            self.test_results.append({
                                'test': 'mt5_connection',
                                'passed': False,
                                'error': 'Failed to get account info'
                            })
                    else:
                        print("❌ MT5 login failed")
                        self.test_results.append({
                            'test': 'mt5_connection',
                            'passed': False,
                            'error': 'Login failed'
                        })
                else:
                    print("❌ Account configuration not found")
                    self.test_results.append({
                        'test': 'mt5_connection',
                        'passed': False,
                        'error': 'Account config not found'
                    })
            else:
                print("❌ MT5 initialization failed")
                self.test_results.append({
                    'test': 'mt5_connection',
                    'passed': False,
                    'error': 'Initialization failed'
                })
                
        except Exception as e:
            print(f"❌ MT5 connection error: {e}")
            self.test_results.append({
                'test': 'mt5_connection',
                'passed': False,
                'error': str(e)
            })
            
    async def _test_money_management_integration(self):
        """Test money management integration with real account data"""
        print("\n💰 Testing Money Management Integration")
        print("-" * 40)
        
        try:
            # Get real account info if MT5 is connected
            if self.mt5_client and self.mt5_client.current_account:
                account_info = self.mt5_client.get_account_info()
                if account_info:
                    print(f"Using real account data: ${account_info.balance:.2f}")
                else:
                    # Fallback to demo data
                    account_info = AccountInfo(
                        balance=74.40,
                        equity=74.40,
                        margin=0.0,
                        free_margin=74.40,
                        margin_level=0.0,
                        currency="USD",
                        leverage=500
                    )
                    print("Using demo account data")
            else:
                # Use demo data
                account_info = AccountInfo(
                    balance=74.40,
                    equity=74.40,
                    margin=0.0,
                    free_margin=74.40,
                    margin_level=0.0,
                    currency="USD",
                    leverage=500
                )
                print("Using demo account data")
            
            # Test percent risk strategy
            strategy = PercentRiskStrategy({'risk_percent': 2.0})
            
            # Test with different scenarios
            test_scenarios = [
                {
                    'symbol': 'EURUSD',
                    'entry_price': 1.0850,
                    'stop_loss': 1.0820,
                    'description': 'EURUSD 30 pip stop'
                },
                {
                    'symbol': 'EURUSD',
                    'entry_price': 1.0850,
                    'stop_loss': None,
                    'description': 'EURUSD no stop loss'
                }
            ]
            
            market_data = {
                'pip_value': 10.0,
                'pip_size': 0.0001,
                'min_volume': 0.01,
                'max_volume': 100.0
            }
            
            all_passed = True
            
            for scenario in test_scenarios:
                print(f"\n  {scenario['description']}:")
                
                trade_params = strategy.calculate_position_size(
                    account_info=account_info,
                    symbol=scenario['symbol'],
                    entry_price=scenario['entry_price'],
                    stop_loss=scenario['stop_loss'],
                    trade_history=[],
                    market_data=market_data
                )
                
                print(f"    Volume: {trade_params.volume:.2f}")
                print(f"    Risk Amount: ${trade_params.risk_amount:.2f}")
                print(f"    Stop Loss: {trade_params.stop_loss}")
                
                # Validate results
                has_volume = trade_params.volume > 0
                has_risk = trade_params.risk_amount > 0
                reasonable_risk = trade_params.risk_amount <= account_info.balance * 0.2  # Max 20%
                
                scenario_passed = has_volume and has_risk and reasonable_risk
                
                print(f"    Has Volume: {'✅' if has_volume else '❌'}")
                print(f"    Has Risk: {'✅' if has_risk else '❌'}")
                print(f"    Reasonable Risk: {'✅' if reasonable_risk else '❌'}")
                print(f"    Result: {'✅ PASS' if scenario_passed else '❌ FAIL'}")
                
                if not scenario_passed:
                    all_passed = False
            
            self.test_results.append({
                'test': 'money_management_integration',
                'passed': all_passed
            })
            
        except Exception as e:
            print(f"❌ Money management integration error: {e}")
            self.test_results.append({
                'test': 'money_management_integration',
                'passed': False,
                'error': str(e)
            })
            
    async def _test_risk_management_enforcement(self):
        """Test risk management enforcement with real data"""
        print("\n🛡️ Testing Risk Management Enforcement")
        print("-" * 40)
        
        try:
            # Load account settings
            accounts_file = os.path.join('config', 'accounts.json')
            with open(accounts_file, 'r') as f:
                accounts_data = json.load(f)
            
            account_config = accounts_data['accounts'][0]
            risk_settings = account_config['money_management_settings']
            
            print(f"Max Daily Loss: ${risk_settings['max_daily_loss']}")
            print(f"Max Daily Trades: {risk_settings['max_daily_trades']}")
            print(f"Max Open Positions: {risk_settings['max_open_positions']}")
            
            # Test daily loss enforcement
            daily_loss_scenarios = [
                {'daily_pnl': -2.0, 'should_allow': True},
                {'daily_pnl': -3.0, 'should_allow': False},
                {'daily_pnl': -5.0, 'should_allow': False}
            ]
            
            all_passed = True
            
            for scenario in daily_loss_scenarios:
                loss_exceeded = scenario['daily_pnl'] <= -risk_settings['max_daily_loss']
                can_trade = not loss_exceeded
                
                is_correct = can_trade == scenario['should_allow']
                
                print(f"  Daily P&L ${scenario['daily_pnl']:.1f}: {'ALLOW' if can_trade else 'BLOCK'} ({'✅' if is_correct else '❌'})")
                
                if not is_correct:
                    all_passed = False
            
            self.test_results.append({
                'test': 'risk_management_enforcement',
                'passed': all_passed
            })
            
        except Exception as e:
            print(f"❌ Risk management enforcement error: {e}")
            self.test_results.append({
                'test': 'risk_management_enforcement',
                'passed': False,
                'error': str(e)
            })
            
    async def _test_system_safety_checks(self):
        """Test system safety checks"""
        print("\n🔒 Testing System Safety Checks")
        print("-" * 40)
        
        safety_checks = [
            {
                'name': 'Environment Variables',
                'check': lambda: os.getenv('OPENAI_API_KEY') is not None,
                'description': 'OpenAI API key configured'
            },
            {
                'name': 'Account Configuration',
                'check': lambda: os.path.exists('config/accounts.json'),
                'description': 'Account configuration file exists'
            },
            {
                'name': 'Log Directory',
                'check': lambda: os.path.exists('logs'),
                'description': 'Log directory exists'
            },
            {
                'name': 'Source Code',
                'check': lambda: os.path.exists('src'),
                'description': 'Source code directory exists'
            }
        ]
        
        all_passed = True
        
        for check in safety_checks:
            try:
                result = check['check']()
                print(f"  {check['name']}: {'✅ PASS' if result else '❌ FAIL'}")
                
                if not result:
                    all_passed = False
                    
            except Exception as e:
                print(f"  {check['name']}: ❌ ERROR ({e})")
                all_passed = False
        
        self.test_results.append({
            'test': 'system_safety_checks',
            'passed': all_passed
        })
        
    def _analyze_results(self):
        """Analyze end-to-end test results"""
        print("\n🔍 END-TO-END ANALYSIS")
        print("=" * 60)
        
        # Group results by test type
        test_groups = {}
        for result in self.test_results:
            test_type = result['test']
            if test_type not in test_groups:
                test_groups[test_type] = []
            test_groups[test_type].append(result)
        
        overall_pass = True
        critical_failures = []
        
        for test_type, results in test_groups.items():
            print(f"\n📈 {test_type.replace('_', ' ').title()}:")
            
            passed_tests = [r for r in results if r.get('passed', False)]
            total_tests = len(results)
            pass_rate = len(passed_tests) / total_tests if total_tests > 0 else 0
            
            print(f"  Passed: {len(passed_tests)}/{total_tests} ({pass_rate:.1%})")
            
            if pass_rate < 1.0:
                overall_pass = False
                failed_tests = [r for r in results if not r.get('passed', False)]
                for failed in failed_tests:
                    error_msg = failed.get('error', 'Unknown error')
                    print(f"    ❌ {error_msg}")
                    
                    # Mark critical failures
                    if test_type in ['account_loading', 'mt5_connection']:
                        critical_failures.append(test_type)
            else:
                print(f"    ✅ All tests passed")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        
        if critical_failures:
            print(f"❌ CRITICAL FAILURES: {critical_failures}")
            print("⚠️  System is NOT ready for production trading")
        elif overall_pass:
            print("✅ ALL TESTS PASSED")
            print("🎉 System appears ready for production trading")
        else:
            print("⚠️  SOME TESTS FAILED")
            print("🔧 System needs fixes before production trading")
        
        return overall_pass and not critical_failures

async def main():
    """Main test function"""
    validator = EndToEndValidator()
    
    try:
        await validator.run_comprehensive_validation()
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
