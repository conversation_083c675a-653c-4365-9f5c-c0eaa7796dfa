#!/usr/bin/env python3
"""
Comprehensive analysis of all potential risk amplification sources
"""

import sys
sys.path.append('src')

from money_management.percent_risk import PercentRiskStrategy
from money_management.fixed_volume import FixedVolumeStrategy
from money_management.anti_martingale import AntiMartingaleStrategy
from money_management.base_strategy import AccountInfo

def test_percent_risk_amplification():
    """Test percent risk for potential amplification"""
    print("=" * 60)
    print("TESTING PERCENT RISK AMPLIFICATION")
    print("=" * 60)
    
    balance = 74.40
    account_info = AccountInfo(balance, balance, 0, balance/2, 1000, "USD", 500)
    
    # Test extreme scenarios
    test_cases = [
        {"risk_percent": 0.5, "name": "Conservative"},
        {"risk_percent": 2.0, "name": "Moderate"},
        {"risk_percent": 5.0, "name": "Aggressive"},
        {"risk_percent": 10.0, "name": "Very Aggressive"},
    ]
    
    market_data = {"pip_value": 10.0, "pip_size": 0.0001, "min_volume": 0.01, "max_volume": 100.0}
    
    for case in test_cases:
        strategy = PercentRiskStrategy(case)
        trade_params = strategy.calculate_position_size(
            account_info, "EURUSD", 1.1000, 1.0950, [], market_data
        )
        
        expected_risk = balance * (case['risk_percent'] / 100)
        actual_risk = trade_params.risk_amount
        amplification = actual_risk / expected_risk if expected_risk > 0 else float('inf')
        
        print(f"{case['name']} ({case['risk_percent']}%):")
        print(f"  Expected Risk: ${expected_risk:.2f}")
        print(f"  Actual Risk: ${actual_risk:.2f}")
        print(f"  Amplification: {amplification:.1f}x")
        print(f"  Volume: {trade_params.volume:.2f} lots")
        print()

def test_anti_martingale_amplification():
    """Test anti-martingale for potential amplification"""
    print("=" * 60)
    print("TESTING ANTI-MARTINGALE AMPLIFICATION")
    print("=" * 60)
    
    balance = 1000.0
    account_info = AccountInfo(balance, balance, 0, balance/2, 1000, "USD", 500)
    
    # Simulate consecutive wins
    win_scenarios = [0, 1, 2, 3, 4, 5]
    
    config = {"base_volume": 0.01, "max_multiplier": 4}
    strategy = AntiMartingaleStrategy(config)
    market_data = {"pip_value": 10.0, "pip_size": 0.0001, "min_volume": 0.01, "max_volume": 100.0}
    
    for wins in win_scenarios:
        # Create fake trade history with wins
        trade_history = []
        for i in range(wins):
            trade_history.append({
                'symbol': 'EURUSD',
                'profit': 10.0,  # Winning trade
                'close_time': f"2024-01-{i+1:02d}"
            })
        
        trade_params = strategy.calculate_position_size(
            account_info, "EURUSD", 1.1000, 1.0950, trade_history, market_data
        )
        
        base_risk = 50 * 10.0 * 0.01  # 50 pips * $10 * 0.01 lots = $5
        actual_risk = trade_params.risk_amount
        amplification = actual_risk / base_risk if base_risk > 0 else float('inf')
        
        print(f"After {wins} consecutive wins:")
        print(f"  Volume: {trade_params.volume:.2f} lots")
        print(f"  Risk: ${actual_risk:.2f}")
        print(f"  Amplification vs base: {amplification:.1f}x")
        print()

def test_multiple_tp_amplification():
    """Test multiple TP for risk amplification"""
    print("=" * 60)
    print("TESTING MULTIPLE TP AMPLIFICATION")
    print("=" * 60)
    
    balance = 74.40
    account_info = AccountInfo(balance, balance, 0, balance/2, 1000, "USD", 500)
    
    config = {"risk_percent": 2.0}
    strategy = PercentRiskStrategy(config)
    market_data = {"pip_value": 10.0, "pip_size": 0.0001, "min_volume": 0.01, "max_volume": 100.0}
    
    # Calculate base trade
    base_params = strategy.calculate_position_size(
        account_info, "EURUSD", 1.1000, 1.0950, [], market_data
    )
    
    # Simulate multiple TP levels
    tp_levels = [
        {"volume_percent": 50},
        {"volume_percent": 30}, 
        {"volume_percent": 20}
    ]
    
    total_volume = 0
    total_risk = 0
    
    print(f"Base calculation:")
    print(f"  Single trade volume: {base_params.volume:.2f} lots")
    print(f"  Single trade risk: ${base_params.risk_amount:.2f}")
    print()
    
    print("Multiple TP simulation:")
    for i, tp in enumerate(tp_levels, 1):
        # Each TP level gets its own calculation
        tp_volume = round(base_params.volume * (tp['volume_percent'] / 100), 2)
        if tp_volume < market_data['min_volume']:
            tp_volume = market_data['min_volume']
        
        tp_risk = 50 * market_data['pip_value'] * tp_volume  # 50 pips
        total_volume += tp_volume
        total_risk += tp_risk
        
        print(f"  TP{i} ({tp['volume_percent']}%): {tp_volume:.2f} lots, ${tp_risk:.2f} risk")
    
    amplification = total_risk / base_params.risk_amount
    print(f"\nTotal multiple TP:")
    print(f"  Total volume: {total_volume:.2f} lots")
    print(f"  Total risk: ${total_risk:.2f}")
    print(f"  Amplification: {amplification:.1f}x")

def test_fixed_volume_safety():
    """Test fixed volume for safety"""
    print("\n" + "=" * 60)
    print("TESTING FIXED VOLUME SAFETY")
    print("=" * 60)
    
    balance = 74.40
    account_info = AccountInfo(balance, balance, 0, balance/2, 1000, "USD", 500)
    
    # Test different fixed volumes
    volumes = [0.01, 0.05, 0.10, 0.50]
    market_data = {"pip_value": 10.0, "pip_size": 0.0001, "min_volume": 0.01, "max_volume": 100.0}
    
    for vol in volumes:
        config = {"fixed_volume": vol}
        strategy = FixedVolumeStrategy(config)
        
        trade_params = strategy.calculate_position_size(
            account_info, "EURUSD", 1.1000, 1.0950, [], market_data
        )
        
        risk_percent = (trade_params.risk_amount / balance) * 100
        
        print(f"Fixed volume {vol:.2f} lots:")
        print(f"  Risk: ${trade_params.risk_amount:.2f}")
        print(f"  Risk %: {risk_percent:.1f}%")
        print(f"  Safe: {'YES' if risk_percent <= 10 else 'NO'}")
        print()

def test_risk_validation_limits():
    """Test risk validation limits"""
    print("=" * 60)
    print("TESTING RISK VALIDATION LIMITS")
    print("=" * 60)
    
    balance = 74.40
    
    # Test scenarios that should be blocked
    test_cases = [
        {"risk_amount": 3.72, "description": "5% risk"},
        {"risk_amount": 7.44, "description": "10% risk"},
        {"risk_amount": 14.88, "description": "20% risk"},
        {"risk_amount": 37.20, "description": "50% risk"},
        {"risk_amount": 74.40, "description": "100% risk"},
    ]
    
    for case in test_cases:
        # Base validation: max 50% of balance
        base_limit = balance * 0.5
        passes_base = case['risk_amount'] <= base_limit
        
        # Account-specific validation: max 10% per trade
        account_limit = balance * 0.10
        passes_account = case['risk_amount'] <= account_limit
        
        # 2x configured risk validation (2% = $1.49, so 2x = $2.98)
        configured_risk = balance * 0.02
        max_allowed = configured_risk * 2
        passes_configured = case['risk_amount'] <= max_allowed
        
        print(f"{case['description']} (${case['risk_amount']:.2f}):")
        print(f"  Base validation (≤50%): {'PASS' if passes_base else 'FAIL'}")
        print(f"  Account validation (≤10%): {'PASS' if passes_account else 'FAIL'}")
        print(f"  Configured validation (≤2x): {'PASS' if passes_configured else 'FAIL'}")
        print(f"  Overall: {'PASS' if all([passes_base, passes_account, passes_configured]) else 'FAIL'}")
        print()

if __name__ == "__main__":
    print("COMPREHENSIVE RISK AMPLIFICATION ANALYSIS")
    print("=" * 60)
    
    test_percent_risk_amplification()
    test_anti_martingale_amplification()
    test_multiple_tp_amplification()
    test_fixed_volume_safety()
    test_risk_validation_limits()
    
    print("=" * 60)
    print("ANALYSIS COMPLETE")
    print("=" * 60)
