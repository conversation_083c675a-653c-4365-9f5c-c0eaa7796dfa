#!/usr/bin/env python3
"""
Test Qwen International API with correct endpoint and format
"""

import asyncio
import os
import aiohttp
import json
from dotenv import load_dotenv

async def test_qwen_international():
    """Test Qwen International API with OpenAI-compatible format"""
    print("🔍 Testing Qwen International API...")
    
    # Load environment variables
    load_dotenv()
    
    # Get API configuration
    api_key = os.getenv('QWEN_API_KEY')
    api_url = os.getenv('QWEN_API_URL', 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1')
    model = os.getenv('AI_MODEL', 'qwen-max-2025-01-25')
    
    if not api_key:
        print("❌ QWEN_API_KEY not found")
        return False
    
    # Ensure URL has the correct endpoint
    if not api_url.endswith('/chat/completions'):
        api_url = api_url.rstrip('/') + '/chat/completions'
    
    print(f"✓ API Key: {api_key[:10]}...{api_key[-4:]}")
    print(f"✓ API URL: {api_url}")
    print(f"✓ Model: {model}")
    
    # Prepare test request (OpenAI-compatible format)
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful AI assistant. Respond with valid JSON format."
            },
            {
                "role": "user",
                "content": "Please respond with this exact JSON: {\"status\": \"connected\", \"message\": \"Qwen API test successful\", \"model\": \"" + model + "\"}"
            }
        ],
        "temperature": 0.1,
        "max_tokens": 150
    }
    
    try:
        print("📡 Sending test request...")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, headers=headers, json=payload) as response:
                print(f"📊 Response Status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ API Response received:")
                    
                    # Extract the response text (OpenAI-compatible format)
                    choices = result.get('choices', [])
                    if choices:
                        message = choices[0].get('message', {})
                        content = message.get('content', '')
                        print(f"Response: {content}")
                        
                        # Try to parse as JSON
                        try:
                            parsed = json.loads(content)
                            if parsed.get('status') == 'connected':
                                print("🎉 JSON parsing successful!")
                                return True
                        except:
                            print("⚠️ Response received but not valid JSON")
                            return True  # Still a successful connection
                    else:
                        print(f"Unexpected response format: {result}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ API Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

async def test_trading_prompt():
    """Test with a trading-specific prompt"""
    print("\n🔍 Testing Trading-Specific Prompt...")
    
    load_dotenv()
    
    api_key = os.getenv('QWEN_API_KEY')
    api_url = os.getenv('QWEN_API_URL', 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1')
    model = os.getenv('AI_MODEL', 'qwen-max-2025-01-25')
    
    if not api_url.endswith('/chat/completions'):
        api_url = api_url.rstrip('/') + '/chat/completions'
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    trading_prompt = """
    Analyze this market data and provide a trading decision:
    
    Symbol: EURUSD
    Current Price: 1.0850
    24h High: 1.0875
    24h Low: 1.0820
    RSI: 65
    Moving Average (20): 1.0840
    
    Please respond with JSON format:
    {
        "action": "BUY",
        "confidence": 0.7,
        "reasoning": "Brief analysis",
        "risk_level": "MEDIUM"
    }
    """
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "system",
                "content": "You are an expert forex trading AI. Always respond in valid JSON format with trading recommendations."
            },
            {
                "role": "user",
                "content": trading_prompt
            }
        ],
        "temperature": 0.3,
        "max_tokens": 500
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    choices = result.get('choices', [])
                    if choices:
                        content = choices[0].get('message', {}).get('content', '')
                        print(f"Trading Response: {content}")
                        return True
                else:
                    error = await response.text()
                    print(f"❌ Trading test failed: {error}")
                    return False
    except Exception as e:
        print(f"❌ Trading test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🤖 Qwen International API Test")
    print("=" * 50)
    
    # Test basic connection
    basic_success = await test_qwen_international()
    
    if basic_success:
        print("\n✅ Basic API connection successful!")
        
        # Test trading prompt
        trading_success = await test_trading_prompt()
        
        if trading_success:
            print("\n🎉 Trading API test successful!")
            print("✅ Your Qwen API is fully functional for trading")
            print("✅ The trading system is ready to use AI decisions")
        else:
            print("\n⚠️ Basic connection works, but trading test had issues")
    else:
        print("\n❌ API connection failed")
        print("Please verify:")
        print("- API key is correct and active")
        print("- You have internet connection")
        print("- Your Qwen account has sufficient credits")
    
    return basic_success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
