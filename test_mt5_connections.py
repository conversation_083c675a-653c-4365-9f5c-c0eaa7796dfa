#!/usr/bin/env python3
"""
Comprehensive MT5 Connection and Data Retrieval Test
Tests all MT5 functionality to ensure proper data flow
"""

import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from mt5_integration.mt5_client import MT5Client
from account_management.models import TradingAccount
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

def load_test_account() -> TradingAccount:
    """Load the first account from config for testing"""
    try:
        with open('config/accounts.json', 'r') as f:
            accounts_config = json.load(f)
        
        first_account = accounts_config['accounts'][0]
        return TradingAccount(
            account_id=first_account['account_id'],
            account_number=first_account['account_number'],
            server=first_account['server'],
            username=first_account.get('username', ''),
            password=first_account['password'],
            strategy_type=first_account.get('strategy_type', ''),
            money_management_type=first_account.get('money_management_type', ''),
            symbols=first_account.get('symbols', []),
            timeframes=first_account.get('timeframes', []),
            money_management_config=first_account.get('money_management_config', {})
        )
    except Exception as e:
        logger.error(f"Error loading test account: {e}")
        return None

def test_mt5_initialization():
    """Test MT5 initialization"""
    print("\n" + "="*60)
    print("🔧 TESTING MT5 INITIALIZATION")
    print("="*60)
    
    client = MT5Client()
    
    # Test initialization
    if client.initialize():
        print("✅ MT5 initialization: SUCCESS")
        return client
    else:
        print("❌ MT5 initialization: FAILED")
        return None

def test_account_login(client: MT5Client, account: TradingAccount):
    """Test account login"""
    print("\n" + "="*60)
    print("🔐 TESTING ACCOUNT LOGIN")
    print("="*60)
    
    if client.login(account):
        print(f"✅ Account login: SUCCESS - {account.account_number}")
        return True
    else:
        print(f"❌ Account login: FAILED - {account.account_number}")
        return False

def test_account_info(client: MT5Client):
    """Test account information retrieval"""
    print("\n" + "="*60)
    print("💰 TESTING ACCOUNT INFO RETRIEVAL")
    print("="*60)
    
    account_info = client.get_account_info()
    if account_info:
        print("✅ Account info retrieval: SUCCESS")
        print(f"   📊 Balance: ${account_info.balance:.2f}")
        print(f"   📊 Equity: ${account_info.equity:.2f}")
        print(f"   📊 Margin: ${account_info.margin:.2f}")
        print(f"   📊 Free Margin: ${account_info.free_margin:.2f}")
        print(f"   📊 Margin Level: {account_info.margin_level:.2f}%")
        print(f"   📊 Currency: {account_info.currency}")
        print(f"   📊 Leverage: 1:{account_info.leverage}")
        return True
    else:
        print("❌ Account info retrieval: FAILED")
        return False

def test_market_data(client: MT5Client, symbols: List[str]):
    """Test market data retrieval"""
    print("\n" + "="*60)
    print("📈 TESTING MARKET DATA RETRIEVAL")
    print("="*60)
    
    success_count = 0
    for symbol in symbols[:3]:  # Test first 3 symbols
        print(f"\n🔍 Testing {symbol}...")
        
        market_data = client.get_market_data(symbol, 'H1', 50)
        if market_data:
            print(f"✅ {symbol} market data: SUCCESS")
            print(f"   📊 Current Price: {market_data['current_price']:.5f}")
            print(f"   📊 Spread: {market_data['spread']:.1f} pips")
            print(f"   📊 Volatility: {market_data['volatility']:.5f}")
            print(f"   📊 Candles: {len(market_data['candles'])} bars")
            print(f"   📊 Pip Size: {market_data['pip_size']}")
            print(f"   📊 Min Volume: {market_data['min_volume']}")
            success_count += 1
        else:
            print(f"❌ {symbol} market data: FAILED")
    
    print(f"\n📊 Market Data Summary: {success_count}/{len(symbols[:3])} symbols successful")
    return success_count > 0

def test_positions(client: MT5Client):
    """Test current positions retrieval"""
    print("\n" + "="*60)
    print("📋 TESTING CURRENT POSITIONS")
    print("="*60)

    positions = client.get_positions()
    print(f"✅ Positions retrieval: SUCCESS")
    print(f"   📊 Open Positions: {len(positions)}")

    if positions:
        print("   📋 Current Positions:")
        for i, pos in enumerate(positions[:5], 1):  # Show first 5
            print(f"      {i}. {pos['symbol']} {pos['type']} {pos['volume']} @ {pos['price_open']:.5f}")
            print(f"         Current: {pos['price_current']:.5f} | P&L: ${pos['profit']:.2f}")
    else:
        print("   📋 No open positions found")

    return True

def test_orders(client: MT5Client):
    """Test pending orders retrieval"""
    print("\n" + "="*60)
    print("📋 TESTING PENDING ORDERS")
    print("="*60)

    orders = client.get_orders()
    print(f"✅ Orders retrieval: SUCCESS")
    print(f"   📊 Pending Orders: {len(orders)}")

    if orders:
        print("   📋 Pending Orders:")
        for i, order in enumerate(orders[:5], 1):  # Show first 5
            print(f"      {i}. {order['symbol']} {order['type']} {order['volume']} @ {order['price_open']:.5f}")
            print(f"         State: {order['state']} | Magic: {order['magic']}")
    else:
        print("   📋 No pending orders found")

    return True

def test_trade_history(client: MT5Client):
    """Test trade history retrieval"""
    print("\n" + "="*60)
    print("📚 TESTING TRADE HISTORY")
    print("="*60)
    
    history = client.get_trade_history(days=30)
    print(f"✅ Trade history retrieval: SUCCESS")
    print(f"   📊 Trades in last 30 days: {len(history)}")
    
    if history:
        print("   📋 Recent Trades:")
        for i, trade in enumerate(history[-5:], 1):  # Show last 5
            print(f"      {i}. {trade['symbol']} {trade['type']} {trade['volume']} @ {trade['price']:.5f}")
            print(f"         Time: {trade['time']} | P&L: ${trade['profit']:.2f}")
    else:
        print("   📋 No trade history found")
    
    return True

def test_symbol_info(client: MT5Client, symbols: List[str]):
    """Test symbol information retrieval"""
    print("\n" + "="*60)
    print("🔍 TESTING SYMBOL INFORMATION")
    print("="*60)
    
    import MetaTrader5 as mt5
    
    success_count = 0
    for symbol in symbols[:3]:  # Test first 3 symbols
        print(f"\n🔍 Testing {symbol}...")
        
        # Test symbol info
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            print(f"✅ {symbol} symbol info: SUCCESS")
            print(f"   📊 Digits: {symbol_info.digits}")
            print(f"   📊 Point: {symbol_info.point}")
            print(f"   📊 Min Volume: {symbol_info.volume_min}")
            print(f"   📊 Max Volume: {symbol_info.volume_max}")
            print(f"   📊 Volume Step: {symbol_info.volume_step}")
            success_count += 1
        else:
            print(f"❌ {symbol} symbol info: FAILED")
        
        # Test current tick
        tick = mt5.symbol_info_tick(symbol)
        if tick:
            print(f"✅ {symbol} tick data: SUCCESS")
            print(f"   📊 Bid: {tick.bid:.5f}")
            print(f"   📊 Ask: {tick.ask:.5f}")
            print(f"   📊 Spread: {(tick.ask - tick.bid) / client._get_pip_size(symbol):.1f} pips")
        else:
            print(f"❌ {symbol} tick data: FAILED")
    
    print(f"\n📊 Symbol Info Summary: {success_count}/{len(symbols[:3])} symbols successful")
    return success_count > 0

def main():
    """Main test function"""
    print("🚀 STARTING COMPREHENSIVE MT5 CONNECTION TEST")
    print("=" * 80)
    
    # Load test account
    account = load_test_account()
    if not account:
        print("❌ Failed to load test account")
        return
    
    print(f"🎯 Testing with account: {account.account_number} on {account.server}")
    
    # Get symbols from account config
    symbols = [s['symbol'] for s in account.symbols] if account.symbols else ['EURUSD', 'GBPUSD', 'USDJPY']
    
    # Initialize MT5
    client = test_mt5_initialization()
    if not client:
        return
    
    # Test account login
    if not test_account_login(client, account):
        return
    
    # Test all data retrieval functions
    tests = [
        ("Account Info", lambda: test_account_info(client)),
        ("Market Data", lambda: test_market_data(client, symbols)),
        ("Current Positions", lambda: test_positions(client)),
        ("Pending Orders", lambda: test_orders(client)),
        ("Trade History", lambda: test_trade_history(client)),
        ("Symbol Information", lambda: test_symbol_info(client, symbols)),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("📊 TEST SUMMARY")
    print("="*80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - MT5 integration is fully functional!")
    else:
        print("⚠️  Some tests failed - check MT5 connection and configuration")
    
    # Cleanup
    client.shutdown()

if __name__ == "__main__":
    main()
