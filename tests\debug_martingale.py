#!/usr/bin/env python3
"""
Debug martingale prompt
"""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from money_management.martingale import MartingaleStrategy
from money_management.base_strategy import AccountInfo

def debug_martingale():
    """Debug martingale prompt"""
    
    account_info = AccountInfo(
        balance=10000.0,
        equity=10000.0,
        margin=1000.0,
        free_margin=9000.0,
        margin_level=1000.0,
        currency="USD",
        leverage=100
    )
    
    trade_history = []
    
    strategy = MartingaleStrategy({'base_volume': 0.01, 'max_multiplier': 8})
    prompt = strategy.get_ai_prompt(account_info, trade_history)
    
    print("=== MARTINGALE PROMPT ===")
    print(f"Length: {len(prompt)}")
    print(prompt[:1000])
    
    # Check for martingale concepts
    concepts = [
        "MARTINGALE", "consecutive losses", "multiplier", "recovery",
        "dangerous", "risk", "sequence", "progression"
    ]
    
    print("\n=== CONCEPT ANALYSIS ===")
    for concept in concepts:
        found = concept.lower() in prompt.lower()
        print(f"{concept:20} | Found: {found}")

if __name__ == "__main__":
    debug_martingale()
