#!/usr/bin/env python3
"""
Debug mean reversion prompt
"""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from strategies.mean_reversion import MeanReversionStrategy
from strategies.base_strategy import MarketData

def debug_mr_prompt():
    """Debug mean reversion prompt"""
    
    market_data = MarketData(
        symbol="EURUSD",
        timeframe="H1",
        candles=[],
        current_price=1.1030,
        spread=1.5,
        volume=1100,
        volatility=0.0015
    )
    
    account_info_dict = {'balance': 10000.0, 'equity': 10000.0, 'currency': 'USD'}
    trade_history = []
    
    strategy = MeanReversionStrategy({'magic_number': 12346})
    prompt = strategy.get_ai_prompt(market_data, trade_history, account_info_dict)
    
    print("=== MEAN REVERSION PROMPT ===")
    print(f"Length: {len(prompt)}")
    print(prompt[:1000])
    
    # Check for mean reversion concepts
    mr_concepts = [
        "MEAN REVERSION", "overbought", "oversold", "reversion", "extreme",
        "oscillator", "deviation", "mean", "range", "consolidation"
    ]
    
    print("\n=== CONCEPT ANALYSIS ===")
    for concept in mr_concepts:
        found = concept.lower() in prompt.lower()
        print(f"{concept:15} | Found: {found}")

if __name__ == "__main__":
    debug_mr_prompt()
