{"summary": {"total_tests": 27, "passed": 5, "failed": 8, "errors": 14, "skipped": 0, "success_rate": 18.**************, "total_duration": 4.635481, "start_time": "2025-08-01T13:42:52.471147", "end_time": "2025-08-01T13:42:57.106628"}, "status": "NOT_READY", "test_details": [{"test_name": "test_load_accounts_comprehensive (test_comprehensive_trading_system.TestAccountManagement)", "status": "FAIL", "duration": 0.011901915073394775, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 112, in test_load_accounts_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "timestamp": "2025-08-01T13:42:52.518755"}, {"test_name": "test_money_management_factory_all_types (test_comprehensive_trading_system.TestAccountManagement)", "status": "ERROR", "duration": 0.011901915073394775, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 162, in test_money_management_factory_all_types\n    mm_strategy = self.account_manager.money_management_factory.create_strategy(mm_type)\nTypeError: MoneyManagementFactory.create_strategy() missing 1 required positional argument: 'config'\n", "timestamp": "2025-08-01T13:42:52.518755"}, {"test_name": "test_strategy_factory_all_types (test_comprehensive_trading_system.TestAccountManagement)", "status": "ERROR", "duration": 0.011901915073394775, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 153, in test_strategy_factory_all_types\n    strategy = self.account_manager.strategy_factory.create_strategy(strategy_type)\nTypeError: StrategyFactory.create_strategy() missing 1 required positional argument: 'config'\n", "timestamp": "2025-08-01T13:42:52.518755"}, {"test_name": "Account Management Tests_test_0", "status": "PASS", "duration": 0.011901915073394775, "error_msg": null, "timestamp": "2025-08-01T13:42:52.518755"}, {"test_name": "test_ai_prompts_all_strategies (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0028906822204589843, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.534215"}, {"test_name": "test_anti_martingale_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0028906822204589843, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.534215"}, {"test_name": "test_fixed_volume_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0028906822204589843, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.534215"}, {"test_name": "test_martingale_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0028906822204589843, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.534215"}, {"test_name": "test_percent_risk_strategy_comprehensive (test_comprehensive_trading_system.TestMoneyManagementStrategies)", "status": "ERROR", "duration": 0.0028906822204589843, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.534215"}, {"test_name": "test_trend_following_strategy (test_comprehensive_trading_system.TestTradingStrategies)", "status": "ERROR", "duration": 0.0016017754872639973, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 361, in test_trend_following_strategy\n    valid_signal = TradingSignal(\nTypeError: TradingSignal.__init__() missing 1 required positional argument: 'risk_level'\n", "timestamp": "2025-08-01T13:42:52.542107"}, {"test_name": "Trading Strategy Tests_test_0", "status": "PASS", "duration": 0.0016017754872639973, "error_msg": null, "timestamp": "2025-08-01T13:42:52.542107"}, {"test_name": "Trading Strategy Tests_test_1", "status": "PASS", "duration": 0.0016017754872639973, "error_msg": null, "timestamp": "2025-08-01T13:42:52.542107"}, {"test_name": "test_prompt_builder_comprehensive (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0032719135284423827, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.558467"}, {"test_name": "test_prompt_builder_error_handling (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0032719135284423827, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.558467"}, {"test_name": "test_qwen_client_api_call (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0032719135284423827, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.558467"}, {"test_name": "test_qwen_client_error_handling (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0032719135284423827, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.558467"}, {"test_name": "test_qwen_client_initialization (test_comprehensive_trading_system.TestAIIntegration)", "status": "ERROR", "duration": 0.0032719135284423827, "error_msg": "Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "timestamp": "2025-08-01T13:42:52.558467"}, {"test_name": "test_market_data_retrieval (test_comprehensive_trading_system.TestMT5Integration)", "status": "FAIL", "duration": 0.0057642459869384766, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 584, in test_market_data_retrieval\n    self.assertIsNotNone(market_data)\nAssertionError: unexpectedly None\n", "timestamp": "2025-08-01T13:42:52.575760"}, {"test_name": "test_mt5_login_comprehensive (test_comprehensive_trading_system.TestMT5Integration)", "status": "FAIL", "duration": 0.0057642459869384766, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 561, in test_mt5_login_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "timestamp": "2025-08-01T13:42:52.575760"}, {"test_name": "MT5 Integration Tests_test_0", "status": "PASS", "duration": 0.0057642459869384766, "error_msg": null, "timestamp": "2025-08-01T13:42:52.575760"}, {"test_name": "test_closed_trade_retrieval (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 302, in test_closed_trade_retrieval\n    self.assertEqual(len(trade_history), 3)\nAssertionError: 0 != 3\n", "timestamp": "2025-08-01T13:42:57.106628"}, {"test_name": "test_complete_signal_lifecycle (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1845, in _inner\n    return await f(*args, **kw)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 146, in test_complete_signal_lifecycle\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 898, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n", "timestamp": "2025-08-01T13:42:57.106628"}, {"test_name": "test_multiple_take_profit_execution (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 199, in test_multiple_take_profit_execution\n    self.assertGreaterEqual(mock_mt5.order_send.call_count, 3)\nAssertionError: 0 not greater than or equal to 3\n", "timestamp": "2025-08-01T13:42:57.106628"}, {"test_name": "test_pending_order_management (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 241, in test_pending_order_management\n    self.assertEqual(len(pending_orders), 2)\nAssertionError: 0 != 2\n", "timestamp": "2025-08-01T13:42:57.106628"}, {"test_name": "test_trade_lifecycle_with_ai_management (test_real_world_integration.TestRealWorldSignalExecution)", "status": "FAIL", "duration": 0.****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 389, in test_trade_lifecycle_with_ai_management\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 898, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n", "timestamp": "2025-08-01T13:42:57.106628"}, {"test_name": "test_magic_number_tracking (test_real_world_integration.TestRealWorldSignalExecution)", "status": "ERROR", "duration": 0.****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 83, in _callMaybeAsync\n    ret = func(*args, **kwargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 399, in test_magic_number_tracking\n    strategy = self.account_manager.strategy_factory.create_strategy(strategy_type)\nTypeError: StrategyFactory.create_strategy() missing 1 required positional argument: 'config'\n", "timestamp": "2025-08-01T13:42:57.106628"}, {"test_name": "Real-World Signal Execution Tests_test_0", "status": "PASS", "duration": 0.****************, "error_msg": null, "timestamp": "2025-08-01T13:42:57.106628"}], "failures": ["test_load_accounts_comprehensive (test_comprehensive_trading_system.TestAccountManagement): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 112, in test_load_accounts_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "test_market_data_retrieval (test_comprehensive_trading_system.TestMT5Integration): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 584, in test_market_data_retrieval\n    self.assertIsNotNone(market_data)\nAssertionError: unexpectedly None\n", "test_mt5_login_comprehensive (test_comprehensive_trading_system.TestMT5Integration): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1379, in patched\n    return func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 561, in test_mt5_login_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "test_closed_trade_retrieval (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 302, in test_closed_trade_retrieval\n    self.assertEqual(len(trade_history), 3)\nAssertionError: 0 != 3\n", "test_complete_signal_lifecycle (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1845, in _inner\n    return await f(*args, **kw)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 146, in test_complete_signal_lifecycle\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 898, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n", "test_multiple_take_profit_execution (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 199, in test_multiple_take_profit_execution\n    self.assertGreaterEqual(mock_mt5.order_send.call_count, 3)\nAssertionError: 0 not greater than or equal to 3\n", "test_pending_order_management (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 1396, in patched\n    return await func(*newargs, **newkeywargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 241, in test_pending_order_management\n    self.assertEqual(len(pending_orders), 2)\nAssertionError: 0 != 2\n", "test_trade_lifecycle_with_ai_management (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 87, in _callMaybeAsync\n    return self._asyncioTestLoop.run_until_complete(fut)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\asyncio\\base_events.py\", line 649, in run_until_complete\n    return future.result()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 101, in _asyncioLoopRunner\n    ret = await awaitable\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 389, in test_trade_lifecycle_with_ai_management\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\mock.py\", line 898, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n"], "errors": ["test_money_management_factory_all_types (test_comprehensive_trading_system.TestAccountManagement): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 162, in test_money_management_factory_all_types\n    mm_strategy = self.account_manager.money_management_factory.create_strategy(mm_type)\nTypeError: MoneyManagementFactory.create_strategy() missing 1 required positional argument: 'config'\n", "test_strategy_factory_all_types (test_comprehensive_trading_system.TestAccountManagement): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 153, in test_strategy_factory_all_types\n    strategy = self.account_manager.strategy_factory.create_strategy(strategy_type)\nTypeError: StrategyFactory.create_strategy() missing 1 required positional argument: 'config'\n", "test_ai_prompts_all_strategies (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_anti_martingale_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_fixed_volume_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_martingale_strategy (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_percent_risk_strategy_comprehensive (test_comprehensive_trading_system.TestMoneyManagementStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 171, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_trend_following_strategy (test_comprehensive_trading_system.TestTradingStrategies): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 361, in test_trend_following_strategy\n    valid_signal = TradingSignal(\nTypeError: TradingSignal.__init__() missing 1 required positional argument: 'risk_level'\n", "test_prompt_builder_comprehensive (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_prompt_builder_error_handling (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_qwen_client_api_call (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_qwen_client_error_handling (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_qwen_client_initialization (test_comprehensive_trading_system.TestAIIntegration): Traceback (most recent call last):\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_comprehensive_trading_system.py\", line 425, in setUp\n    self.account_info = AccountInfo(\nTypeError: AccountInfo.__init__() got an unexpected keyword argument 'account_id'\n", "test_magic_number_tracking (test_real_world_integration.TestRealWorldSignalExecution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 64, in _callTestMethod\n    self._callMaybeAsync(method)\n  File \"C:\\Users\\<USER>\\miniconda3\\lib\\unittest\\async_case.py\", line 83, in _callMaybeAsync\n    ret = func(*args, **kwargs)\n  File \"K:\\desktop\\augment\\augment2\\trade\\tests\\test_real_world_integration.py\", line 399, in test_magic_number_tracking\n    strategy = self.account_manager.strategy_factory.create_strategy(strategy_type)\nTypeError: StrategyFactory.create_strategy() missing 1 required positional argument: 'config'\n"], "recommendations": ["🚨 DO NOT deploy to production with current issues", "🚨 Fix all critical errors before proceeding", "🚨 Re-run full test suite after fixes", "📝 Consider code review for failing components", "📝 Test on demo accounts extensively before retry", "📊 Current success rate: 18.5% - Target: 100%"]}