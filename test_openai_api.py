#!/usr/bin/env python3
"""
Test if the API key works with OpenAI API instead
"""

import asyncio
import os
from dotenv import load_dotenv

async def test_openai_api():
    """Test if the key works with OpenAI"""
    print("🔍 Testing if API key works with OpenAI...")
    
    # Load environment variables
    load_dotenv()
    
    api_key = os.getenv('QWEN_API_KEY')
    if not api_key:
        print("❌ No API key found")
        return False
    
    print(f"✓ Testing key: {api_key[:10]}...{api_key[-4:]}")
    
    try:
        from openai import AsyncOpenAI
        
        # Test with OpenAI client
        client = AsyncOpenAI(api_key=api_key)
        
        print("📡 Sending test request to OpenAI...")
        
        response = await client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "user", "content": "Respond with: API test successful"}
            ],
            max_tokens=50
        )
        
        if response.choices:
            content = response.choices[0].message.content
            print(f"✅ OpenAI Response: {content}")
            return True
        else:
            print("❌ No response from OpenAI")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI test failed: {e}")
        return False

async def test_qwen_alternative():
    """Test alternative Qwen API format"""
    print("\n🔍 Testing alternative Qwen API format...")
    
    load_dotenv()
    api_key = os.getenv('QWEN_API_KEY')
    
    try:
        import aiohttp
        
        # Try alternative Qwen endpoint
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # Alternative payload format
        payload = {
            "model": "qwen-turbo",
            "messages": [
                {"role": "user", "content": "Test message"}
            ],
            "max_tokens": 50
        }
        
        async with aiohttp.ClientSession() as session:
            # Try different endpoint
            alt_url = "https://api.openai.com/v1/chat/completions"
            async with session.post(alt_url, headers=headers, json=payload) as response:
                print(f"📊 Alternative endpoint status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ Alternative API works!")
                    return True
                else:
                    error = await response.text()
                    print(f"❌ Alternative failed: {error[:200]}")
                    return False
                    
    except Exception as e:
        print(f"❌ Alternative test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🔍 API Key Compatibility Test")
    print("=" * 40)
    
    # Test OpenAI compatibility
    openai_success = await test_openai_api()
    
    # Test alternative Qwen format
    alt_success = await test_qwen_alternative()
    
    if openai_success:
        print("\n🎉 Your API key works with OpenAI!")
        print("💡 Recommendation: Update the system to use OpenAI API")
        print("   This will provide better compatibility and reliability.")
    elif alt_success:
        print("\n🎉 Alternative Qwen format works!")
    else:
        print("\n❌ API key doesn't work with tested endpoints")
        print("Please verify:")
        print("- The API key is correct and active")
        print("- You have sufficient credits")
        print("- The key format matches the intended service")

if __name__ == "__main__":
    asyncio.run(main())
